HIND SILIGURI BENGALI FONT DOWNLOAD INSTRUCTIONS
===============================================

This app uses Hind Siliguri Bengali fonts for better typography and readability,
matching the same font used in the index.html web version.

REQUIRED FONT FILES:
===================
1. HindSiliguri-Regular.ttf
2. HindSiliguri-Medium.ttf
3. HindSiliguri-SemiBold.ttf
4. HindSiliguri-Bold.ttf

DOWNLOAD SOURCES:
================
1. Google Fonts (Recommended):
   - Visit: https://fonts.google.com/specimen/Hind+Siliguri
   - Click "Download family"
   - Extract the ZIP file
   - Find the 4 required font files

2. Alternative source:
   - Visit: https://github.com/google/fonts/tree/main/ofl/hindsiliguri
   - Download the 4 required TTF files directly

INSTALLATION STEPS:
==================

1. Download the 4 font files mentioned above
2. Rename them exactly as shown (case-sensitive):
   - HindSiliguri-Regular.ttf
   - HindSiliguri-Medium.ttf
   - HindSiliguri-SemiBold.ttf
   - HindSiliguri-Bold.ttf
3. Place all 4 files in this directory: ukilgiri_app/assets/fonts/
4. The fonts are already configured in pubspec.yaml
5. Update the theme in lib/main.dart to use HindSiliguri font
6. Delete this instruction file
7. Run these commands in your terminal:
   - flutter clean
   - flutter pub get
   - flutter run

IMPORTANT NOTES:
===============
- Make sure the font file names match exactly (case-sensitive)
- The fonts must be placed in the assets/fonts/ directory
- After adding fonts, always run 'flutter clean' and 'flutter pub get'
- If fonts don't load, check the pubspec.yaml indentation carefully
- This matches the same font used in the web version (index.html)

TROUBLESHOOTING:
===============
- If fonts don't appear, check the console for font loading errors
- Ensure the font files are not corrupted
- Verify the pubspec.yaml syntax is correct
- Try restarting the app completely after font installation
- Check that font weights (400, 500, 600, 700) are available
