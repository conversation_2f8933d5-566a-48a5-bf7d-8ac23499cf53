import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/auth_service.dart';
import '../theme/app_theme.dart';
import '../widgets/enhanced_components.dart';
import '../main.dart';
import 'admin_dashboard_screen.dart';
import 'dart:math' as math;

class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> with TickerProviderStateMixin {
  final AuthService _authService = AuthService();
  final PageController _pageController = PageController();

  // Animation controllers
  late AnimationController _rotationController;
  late AnimationController _pulseController;

  // Form controllers
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();

  // Form keys
  final GlobalKey<FormState> _signInFormKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _signUpFormKey = GlobalKey<FormState>();

  // State variables
  bool _isSignIn = true;
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _pageController.dispose();
    _rotationController.dispose();
    _pulseController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _nameController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.darkBg,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF0a0a0a),
              AppColors.primaryGreen.withOpacity(0.05),
              AppColors.primaryPurple.withOpacity(0.05),
              const Color(0xFF1a1a1a),
              AppColors.darkBg,
            ],
            stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
          ),
        ),
        child: Stack(
          children: [
            // Animated background particles
            _buildAnimatedBackground(),

            // Floating geometric shapes
            _buildFloatingShapes(),

            // Main content
            SafeArea(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _isSignIn = index == 0;
                    _clearForm();
                  });
                  HapticFeedback.lightImpact();
                },
                children: [
                  _buildSignInPage(),
                  _buildSignUpPage(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build animated background particles
  Widget _buildAnimatedBackground() {
    return Stack(
      children: List.generate(20, (index) {
        return AnimatedBuilder(
          animation: _rotationController,
          builder: (context, child) {
            final offset = Offset(
              (index * 80.0 + _rotationController.value * 150) % MediaQuery.of(context).size.width,
              (index * 50.0 + _rotationController.value * 80) % MediaQuery.of(context).size.height,
            );

            return Positioned(
              left: offset.dx,
              top: offset.dy,
              child: Container(
                width: 2 + (index % 4) * 1.0,
                height: 2 + (index % 4) * 1.0,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: [
                    AppColors.primaryGreen,
                    AppColors.primaryPurple,
                    const Color(0xFFff3e9d),
                    const Color(0xFF0e8aff),
                    Colors.white,
                  ][index % 5].withOpacity(0.3),
                  boxShadow: [
                    BoxShadow(
                      color: [
                        AppColors.primaryGreen,
                        AppColors.primaryPurple,
                        const Color(0xFFff3e9d),
                        const Color(0xFF0e8aff),
                        Colors.white,
                      ][index % 5].withOpacity(0.2),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }),
    );
  }

  // Build floating geometric shapes
  Widget _buildFloatingShapes() {
    return Stack(
      children: List.generate(8, (index) {
        return AnimatedBuilder(
          animation: _pulseController,
          builder: (context, child) {
            final size = MediaQuery.of(context).size;
            final offset = Offset(
              (index * 100.0 + _pulseController.value * 50) % size.width,
              (index * 80.0 + math.sin(_pulseController.value * 2 * math.pi + index) * 30) % size.height,
            );

            return Positioned(
              left: offset.dx,
              top: offset.dy,
              child: Transform.rotate(
                angle: _pulseController.value * 2 * math.pi + index,
                child: Container(
                  width: 20 + (index % 3) * 10,
                  height: 20 + (index % 3) * 10,
                  decoration: BoxDecoration(
                    shape: index % 2 == 0 ? BoxShape.circle : BoxShape.rectangle,
                    borderRadius: index % 2 == 1 ? BorderRadius.circular(4) : null,
                    border: Border.all(
                      color: [
                        AppColors.primaryGreen,
                        AppColors.primaryPurple,
                        const Color(0xFFff3e9d),
                        const Color(0xFF0e8aff),
                      ][index % 4].withOpacity(0.2),
                      width: 1.5,
                    ),
                    gradient: LinearGradient(
                      colors: [
                        [
                          AppColors.primaryGreen,
                          AppColors.primaryPurple,
                          const Color(0xFFff3e9d),
                          const Color(0xFF0e8aff),
                        ][index % 4].withOpacity(0.1),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }

  // Build sign in page
  Widget _buildSignInPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(30),
      child: Column(
        children: [
          const SizedBox(height: 40),

          // Logo and title
          _buildLogo(),

          const SizedBox(height: 50),

          // Sign in form
          Form(
            key: _signInFormKey,
            child: Column(
              children: [
                _buildEmailField(),
                const SizedBox(height: 20),
                _buildPasswordField(),
                const SizedBox(height: 10),
                _buildForgotPasswordButton(),
                const SizedBox(height: 30),
                _buildSignInButton(),
                const SizedBox(height: 20),
                _buildGoogleSignInButton(),
              ],
            ),
          ),

          const SizedBox(height: 30),

          // Switch to sign up
          _buildSwitchToSignUp(),

          // Error message
          if (_errorMessage.isNotEmpty) ...[
            const SizedBox(height: 20),
            _buildErrorMessage(),
          ],
        ],
      ),
    );
  }

  // Build sign up page
  Widget _buildSignUpPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(30),
      child: Column(
        children: [
          const SizedBox(height: 40),

          // Logo and title
          _buildLogo(),

          const SizedBox(height: 50),

          // Sign up form
          Form(
            key: _signUpFormKey,
            child: Column(
              children: [
                _buildNameField(),
                const SizedBox(height: 20),
                _buildEmailField(),
                const SizedBox(height: 8),
                // Gmail requirement info
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.info_outline, color: Colors.blue, size: 16),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'শুধুমাত্র Gmail ইমেইল ঠিকানা গ্রহণযোগ্য',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.textPrimary.withOpacity(0.8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                _buildPasswordField(),
                const SizedBox(height: 20),
                _buildConfirmPasswordField(),
                const SizedBox(height: 30),
                _buildSignUpButton(),
                const SizedBox(height: 20),
                _buildGoogleSignInButton(),
              ],
            ),
          ),

          const SizedBox(height: 30),

          // Switch to sign in
          _buildSwitchToSignIn(),

          // Error message
          if (_errorMessage.isNotEmpty) ...[
            const SizedBox(height: 20),
            _buildErrorMessage(),
          ],
        ],
      ),
    );
  }

  // Build logo
  Widget _buildLogo() {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 + _pulseController.value * 0.05,
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primaryGreen.withOpacity(0.2),
                      AppColors.primaryPurple.withOpacity(0.2),
                    ],
                  ),
                  border: Border.all(
                    color: AppColors.primaryGreen.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildGradientText('Ukil', [AppColors.primaryGreen, AppColors.primaryPurple]),
                    _buildGradientText('Giri', [const Color(0xFFff3e9d), const Color(0xFF0e8aff)]),
                  ],
                ),
              ),
              const SizedBox(height: 10),
              Text(
                _isSignIn ? 'সাইন ইন করুন' : 'নতুন অ্যাকাউন্ট তৈরি করুন',
                style: const TextStyle(
                  fontSize: 24,
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Build gradient text
  Widget _buildGradientText(String text, List<Color> colors) {
    return ShaderMask(
      shaderCallback: (bounds) => LinearGradient(
        colors: colors,
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ).createShader(bounds),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  // Build name field
  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      style: const TextStyle(color: AppColors.textPrimary),
      decoration: InputDecoration(
        labelText: 'নাম',
        labelStyle: const TextStyle(color: AppColors.textPrimary),
        prefixIcon: const Icon(Icons.person, color: AppColors.primaryGreen),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primaryGreen.withOpacity(0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primaryGreen.withOpacity(0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.primaryGreen),
        ),
        filled: true,
        fillColor: Colors.white.withOpacity(0.05),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'নাম লিখুন';
        }
        if (value.length < 2) {
          return 'নাম অন্তত ২টি অক্ষর হতে হবে';
        }
        return null;
      },
    );
  }

  // Build email field
  Widget _buildEmailField() {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: AppColors.primaryGreen.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextFormField(
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            style: const TextStyle(color: AppColors.textPrimary, fontSize: 16),
            decoration: InputDecoration(
              labelText: 'Gmail ইমেইল',
              hintText: '<EMAIL>',
              labelStyle: TextStyle(
                color: AppColors.primaryGreen.withOpacity(0.8),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              hintStyle: TextStyle(
                color: AppColors.textPrimary.withOpacity(0.4),
                fontSize: 14,
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.all(12),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryGreen.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.email_outlined,
                  color: AppColors.primaryGreen,
                  size: 20,
                ),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: BorderSide(color: AppColors.primaryGreen.withOpacity(0.2)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: BorderSide(color: AppColors.primaryGreen.withOpacity(0.2)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: const BorderSide(color: AppColors.primaryGreen, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: const BorderSide(color: Colors.red, width: 1.5),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              filled: true,
              fillColor: Colors.white.withOpacity(0.03),
              contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Gmail ইমেইল ঠিকানা লিখুন';
              }
              if (!RegExp(r'^[\w-\.]+@gmail\.com$').hasMatch(value.toLowerCase())) {
                return 'শুধুমাত্র Gmail ইমেইল ব্যবহার করুন (<EMAIL>)';
              }
              return null;
            },
          ),
        );
      },
    );
  }

  // Build password field
  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      style: const TextStyle(color: AppColors.textPrimary),
      decoration: InputDecoration(
        labelText: 'পাসওয়ার্ড',
        labelStyle: const TextStyle(color: AppColors.textPrimary),
        prefixIcon: const Icon(Icons.lock, color: AppColors.primaryGreen),
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword ? Icons.visibility : Icons.visibility_off,
            color: AppColors.primaryGreen,
          ),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primaryGreen.withOpacity(0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primaryGreen.withOpacity(0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.primaryGreen),
        ),
        filled: true,
        fillColor: Colors.white.withOpacity(0.05),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'পাসওয়ার্ড লিখুন';
        }
        if (value.length < 6) {
          return 'পাসওয়ার্ড অন্তত ৬টি অক্ষর হতে হবে';
        }
        return null;
      },
    );
  }

  // Build confirm password field
  Widget _buildConfirmPasswordField() {
    return TextFormField(
      controller: _confirmPasswordController,
      obscureText: _obscureConfirmPassword,
      style: const TextStyle(color: AppColors.textPrimary),
      decoration: InputDecoration(
        labelText: 'পাসওয়ার্ড নিশ্চিত করুন',
        labelStyle: const TextStyle(color: AppColors.textPrimary),
        prefixIcon: const Icon(Icons.lock_outline, color: AppColors.primaryGreen),
        suffixIcon: IconButton(
          icon: Icon(
            _obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
            color: AppColors.primaryGreen,
          ),
          onPressed: () {
            setState(() {
              _obscureConfirmPassword = !_obscureConfirmPassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primaryGreen.withOpacity(0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primaryGreen.withOpacity(0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.primaryGreen),
        ),
        filled: true,
        fillColor: Colors.white.withOpacity(0.05),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'পাসওয়ার্ড নিশ্চিত করুন';
        }
        if (value != _passwordController.text) {
          return 'পাসওয়ার্ড মিলছে না';
        }
        return null;
      },
    );
  }

  // Build sign in button
  Widget _buildSignInButton() {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Transform.scale(
          scale: _isLoading ? 1.0 + _pulseController.value * 0.02 : 1.0,
          child: Container(
            width: double.infinity,
            height: 55,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              gradient: LinearGradient(
                colors: [
                  AppColors.primaryGreen,
                  AppColors.primaryGreen.withBlue(100),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryGreen.withOpacity(0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: _isLoading ? null : _signIn,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Colors.white,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
              ),
              child: _isLoading
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Text(
                          'সাইন ইন হচ্ছে...',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.login, size: 20),
                        const SizedBox(width: 8),
                        const Text(
                          'সাইন ইন করুন',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        );
      },
    );
  }

  // Build sign up button
  Widget _buildSignUpButton() {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Transform.scale(
          scale: _isLoading ? 1.0 + _pulseController.value * 0.02 : 1.0,
          child: Container(
            width: double.infinity,
            height: 55,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              gradient: LinearGradient(
                colors: [
                  AppColors.primaryPurple,
                  const Color(0xFFff3e9d),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryPurple.withOpacity(0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: _isLoading ? null : _signUp,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Colors.white,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
              ),
              child: _isLoading
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Text(
                          'তৈরি হচ্ছে...',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.person_add, size: 20),
                        const SizedBox(width: 8),
                        const Text(
                          'অ্যাকাউন্ট তৈরি করুন',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        );
      },
    );
  }

  // Build Google sign in button
  Widget _buildGoogleSignInButton() {
    return AnimatedBuilder(
      animation: _rotationController,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 + math.sin(_rotationController.value * 2 * math.pi) * 0.01,
          child: Container(
            width: double.infinity,
            height: 55,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              gradient: LinearGradient(
                colors: [
                  Colors.white.withOpacity(0.1),
                  Colors.white.withOpacity(0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              border: Border.all(
                color: AppColors.primaryGreen.withOpacity(0.3),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.white.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: OutlinedButton(
              onPressed: _isLoading ? null : _signInWithGoogle,
              style: OutlinedButton.styleFrom(
                backgroundColor: Colors.transparent,
                side: BorderSide.none,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: Colors.white,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: Image.network(
                        'https://developers.google.com/identity/images/g-logo.png',
                        width: 20,
                        height: 20,
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(
                            Icons.g_mobiledata,
                            color: Colors.blue,
                            size: 20,
                          );
                        },
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'Google দিয়ে সাইন ইন করুন',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Build forgot password button
  Widget _buildForgotPasswordButton() {
    return Align(
      alignment: Alignment.centerRight,
      child: TextButton(
        onPressed: _showForgotPasswordDialog,
        child: const Text(
          'পাসওয়ার্ড ভুলে গেছেন?',
          style: TextStyle(
            color: AppColors.primaryGreen,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  // Build switch to sign up
  Widget _buildSwitchToSignUp() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text(
          'অ্যাকাউন্ট নেই? ',
          style: TextStyle(color: AppColors.textPrimary),
        ),
        TextButton(
          onPressed: () {
            _pageController.animateToPage(
              1,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          },
          child: const Text(
            'সাইন আপ করুন',
            style: TextStyle(
              color: AppColors.primaryPurple,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  // Build switch to sign in
  Widget _buildSwitchToSignIn() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text(
          'ইতিমধ্যে অ্যাকাউন্ট আছে? ',
          style: TextStyle(color: AppColors.textPrimary),
        ),
        TextButton(
          onPressed: () {
            _pageController.animateToPage(
              0,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          },
          child: const Text(
            'সাইন ইন করুন',
            style: TextStyle(
              color: AppColors.primaryGreen,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  // Build error message
  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          const Icon(Icons.error, color: Colors.red, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _errorMessage,
              style: const TextStyle(
                color: Colors.red,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Authentication methods
  Future<void> _signIn() async {
    if (!_signInFormKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      debugPrint('Starting sign in process...');

      final result = await _authService.signInWithEmailAndPassword(
        email: _emailController.text.trim(),
        password: _passwordController.text,
      );

      debugPrint('Sign in completed, result: ${result != null}');

      // Check if user is signed in (either through result or current user)
      final isSignedIn = result != null || _authService.currentUser != null;

      if (mounted && isSignedIn) {
        debugPrint('User is signed in, checking if admin...');

        // Small delay to ensure Firebase state is updated
        await Future.delayed(const Duration(milliseconds: 500));

        // Check mounted again after delay
        if (mounted) {
          // Check if user is admin
          if (_authService.isAdmin()) {
            debugPrint('User is admin, navigating to admin dashboard');
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const AdminDashboardScreen()),
            );
          } else {
            debugPrint('User is not admin, navigating to home page');
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const UkilGiriHomePage()),
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Sign in error: $e');
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _signUp() async {
    if (!_signUpFormKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      await _authService.signUpWithEmailAndPassword(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        name: _nameController.text.trim(),
      );

      if (mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('অ্যাকাউন্ট তৈরি হয়েছে! ইমেইল যাচাই করুন।'),
            backgroundColor: AppColors.primaryGreen,
            duration: Duration(seconds: 3),
          ),
        );

        // The AuthWrapper will automatically navigate to EmailVerificationScreen
        // since the user is now signed in but email is not verified
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _signInWithGoogle() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });
    }

    try {
      await _authService.signInWithGoogle();

      // Check if user is signed in (even if the method returned null due to type casting error)
      if (mounted && _authService.currentUser != null) {
        // Small delay to ensure Firebase state is updated
        await Future.delayed(const Duration(milliseconds: 500));

        if (mounted) {
          // Check if user is admin
          if (_authService.isAdmin()) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const AdminDashboardScreen()),
            );
          } else {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const UkilGiriHomePage()),
            );
          }
        }
      }
    } catch (e) {
      // Check if user is actually signed in despite the error
      if (mounted && _authService.currentUser != null) {
        // User is signed in despite the error, proceed with navigation
        await Future.delayed(const Duration(milliseconds: 500));

        if (mounted) {
          if (_authService.isAdmin()) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const AdminDashboardScreen()),
            );
          } else {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const UkilGiriHomePage()),
            );
          }
        }
      } else if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showForgotPasswordDialog() {
    final TextEditingController emailController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.darkBg,
        title: const Text(
          'পাসওয়ার্ড রিসেট',
          style: TextStyle(color: AppColors.textPrimary),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'আপনার Gmail ইমেইল ঠিকানা লিখুন। আমরা আপনাকে পাসওয়ার্ড রিসেট লিংক পাঠাবো।',
              style: TextStyle(color: AppColors.textPrimary),
            ),
            const SizedBox(height: 20),
            TextFormField(
              controller: emailController,
              keyboardType: TextInputType.emailAddress,
              style: const TextStyle(color: AppColors.textPrimary),
              decoration: InputDecoration(
                labelText: 'Gmail ইমেইল',
                hintText: '<EMAIL>',
                labelStyle: const TextStyle(color: AppColors.textPrimary),
                hintStyle: TextStyle(color: AppColors.textPrimary.withOpacity(0.5)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: AppColors.primaryGreen.withOpacity(0.3)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.primaryGreen),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'বাতিল',
              style: TextStyle(color: AppColors.textPrimary),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              if (emailController.text.trim().isNotEmpty) {
                final navigator = Navigator.of(context);
                final scaffoldMessenger = ScaffoldMessenger.of(context);

                try {
                  await _authService.resetPassword(emailController.text.trim());
                  if (mounted) {
                    navigator.pop();
                    scaffoldMessenger.showSnackBar(
                      const SnackBar(
                        content: Text('পাসওয়ার্ড রিসেট লিংক পাঠানো হয়েছে'),
                        backgroundColor: AppColors.primaryGreen,
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text(e.toString()),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryGreen,
            ),
            child: const Text(
              'পাঠান',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _clearForm() {
    _emailController.clear();
    _passwordController.clear();
    _nameController.clear();
    _confirmPasswordController.clear();
    setState(() {
      _errorMessage = '';
    });
  }
}