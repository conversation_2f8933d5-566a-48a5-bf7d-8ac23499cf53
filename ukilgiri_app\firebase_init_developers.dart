// Firebase Initialization Script for Developer Data
// Run this script once to initialize the developer data in Firestore

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';

Future<void> main() async {
  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  final firestore = FirebaseFirestore.instance;

  try {
    // Initialize developer data
    await firestore.collection('admin_settings').doc('developers').set({
      'developer1': {
        'name': '<PERSON><PERSON><PERSON>',
        'description': 'Lead Developer & AI Specialist with expertise in Flutter development and machine learning integration. Passionate about creating innovative solutions that bridge technology and user needs.',
        'website': 'https://salauddin.dev',
      },
      'developer2': {
        'name': 'Meheron Nesa Surovi',
        'description': 'UI/UX Designer & Frontend Developer specializing in user experience and modern interface design. Expert in creating intuitive and beautiful user interfaces.',
        'website': 'https://surovi.dev',
      },
      'lastUpdated': FieldValue.serverTimestamp(),
      'updatedBy': 'system_init',
    });

    print('✅ Developer data initialized successfully!');
    print('📄 Collection: admin_settings');
    print('📄 Document: developers');
    print('👨‍💻 Developer 1: Salauddin Majumder');
    print('👩‍💻 Developer 2: Meheron Nesa Surovi');
    
  } catch (e) {
    print('❌ Error initializing developer data: $e');
  }
}
