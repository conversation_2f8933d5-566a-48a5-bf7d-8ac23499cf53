class PaymentConfig {
  // bKash Configuration
  // NOTE: Replace these with your actual bKash credentials
  static const String bkashAppKey = 'YOUR_BKASH_APP_KEY';
  static const String bkashAppSecret = 'YOUR_BKASH_APP_SECRET';
  static const String bkashUsername = 'YOUR_BKASH_USERNAME';
  static const String bkashPassword = 'YOUR_BKASH_PASSWORD';
  
  // Environment URLs
  static const String bkashSandboxUrl = 'https://tokenized.sandbox.bka.sh/v1.2.0-beta';
  static const String bkashProductionUrl = 'https://tokenized.pay.bka.sh/v1.2.0-beta';
  
  // Use sandbox for testing, production for live
  static const bool isProduction = false;
  
  static String get baseUrl => isProduction ? bkashProductionUrl : bkashSandboxUrl;
  
  // Premium plan configuration
  static const double premiumPrice = 100.0; // 100 Taka
  static const int premiumDurationDays = 30; // 30 days
  
  // Payment callback URL (replace with your actual domain)
  static const String callbackUrl = 'https://your-app.com/payment/callback';
  
  // Test credentials for sandbox (these are dummy values)
  static const String testMerchantNumber = '01770618567';
  static const String testCustomerNumber = '01770618568';
}
