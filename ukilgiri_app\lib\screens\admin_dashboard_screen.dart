import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'dart:ui';
import 'dart:async';
import '../services/auth_service.dart';
import '../theme/app_theme.dart';
import '../widgets/enhanced_components.dart';
import 'auth_screen.dart';

// Modern Color Palette for Professional Dashboard
class DashboardColors {
  static const Color primaryBlue = Color(0xFF2196F3);
  static const Color primaryPurple = Color(0xFF9C27B0);
  static const Color primaryGreen = Color(0xFF4CAF50);
  static const Color primaryOrange = Color(0xFFFF9800);
  static const Color primaryRed = Color(0xFFF44336);
  static const Color primaryTeal = Color(0xFF009688);
  static const Color primaryIndigo = Color(0xFF3F51B5);
  static const Color primaryPink = Color(0xFFE91E63);

  static const Color cardBg = Color(0xFF1E1E2E);
  static const Color surfaceBg = Color(0xFF181825);
  static const Color accentBg = Color(0xFF313244);
  static const Color textPrimary = Color(0xFFCDD6F4);
  static const Color textSecondary = Color(0xFFA6ADC8);
  static const Color textMuted = Color(0xFF6C7086);

  static const Color success = Color(0xFFA6E3A1);
  static const Color warning = Color(0xFFF9E2AF);
  static const Color error = Color(0xFFF38BA8);
  static const Color info = Color(0xFF89B4FA);
}

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> with TickerProviderStateMixin {
  final AuthService _authService = AuthService();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Animation controllers
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late AnimationController _slideController;

  // Dashboard data - now using streams for real-time updates
  int _totalUsers = 0;
  int _totalChats = 0;
  int _premiumUsers = 0;
  int _todayChats = 0;
  int _activeUsers = 0;
  int _totalRevenue = 0;
  int _weeklyChats = 0;
  int _monthlyChats = 0;
  int _totalCharacters = 0;
  int _todayCharacters = 0;
  int _weeklyCharacters = 0;
  int _monthlyCharacters = 0;
  bool _isLoading = true;

  // Real-time stream subscriptions
  late StreamSubscription<QuerySnapshot> _usersSubscription;
  late StreamSubscription<QuerySnapshot> _paymentsSubscription;

  // API Configuration
  final TextEditingController _apiKeyController = TextEditingController();
  final TextEditingController _apiUrlController = TextEditingController();

  // Notification management state variables
  final TextEditingController _notificationTitleController = TextEditingController();
  final TextEditingController _notificationMessageController = TextEditingController();
  final TextEditingController _notificationFrequencyController = TextEditingController();
  final TextEditingController _hyperlinkTextController = TextEditingController();
  final TextEditingController _hyperlinkUrlController = TextEditingController();
  bool _isNotificationEnabled = false;

  // Banner text editor state variables
  final TextEditingController _bannerTextController = TextEditingController();
  final TextEditingController _bannerColorWordsController = TextEditingController();
  final TextEditingController _bannerHyperlinkTextController = TextEditingController();
  final TextEditingController _bannerHyperlinkUrlController = TextEditingController();
  final TextEditingController _bannerScrollSpeedController = TextEditingController();

  // Tab controller for different sections
  late TabController _tabController;

  // Real-time data streams
  late Stream<QuerySnapshot> _usersStream;
  late Stream<QuerySnapshot> _paymentsStream;

  // Selected tab index
  int _selectedTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeTabController();
    _initializeStreams();
    _setupRealTimeListeners();
    _loadApiConfiguration();
    _loadNotificationSettings(); // Load notification settings on init

    // Add a timeout to ensure loading doesn't persist indefinitely
    Timer(const Duration(seconds: 10), () {
      if (mounted && _isLoading) {
        debugPrint('Dashboard loading timeout - forcing loading to false');
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat(reverse: true);

    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    )..repeat();

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);
  }

  void _initializeTabController() {
    _tabController = TabController(length: 5, vsync: this);
  }

  void _initializeStreams() {
    _usersStream = _firestore.collection('users').snapshots();
    _paymentsStream = _firestore.collection('payments').snapshots();
  }

  void _setupRealTimeListeners() {
    // Listen to users collection for real-time updates
    _usersSubscription = _firestore.collection('users').snapshots().listen(
      (snapshot) {
        _updateDashboardDataFromUsers(snapshot);
      },
      onError: (error) {
        debugPrint('Error listening to users collection: $error');
        // Set loading to false even if there's an error
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      },
    );

    // Listen to payments collection for real-time updates
    _paymentsSubscription = _firestore.collection('payments').snapshots().listen(
      (snapshot) {
        _updateRevenueData(snapshot);
      },
      onError: (error) {
        debugPrint('Error listening to payments collection: $error');
      },
    );
  }

  void _updateDashboardDataFromUsers(QuerySnapshot snapshot) {
    if (!mounted) return;

    try {
      int totalUsers = snapshot.docs.length;
      int premiumUsers = 0;
      int activeUsers = 0;
      int totalChats = 0;
      int weeklyChats = 0;
      int monthlyChats = 0;
      int todayChats = 0;
      int totalCharacters = 0;
      int weeklyCharacters = 0;
      int monthlyCharacters = 0;
      int todayCharacters = 0;

      final now = DateTime.now();
      final today = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
      final todayStart = DateTime(now.year, now.month, now.day);
      final weekStart = now.subtract(const Duration(days: 7));
      final monthStart = DateTime(now.year, now.month - 1, now.day);
      final weekAgo = DateTime.now().subtract(const Duration(days: 7));

      for (var userDoc in snapshot.docs) {
        try {
          final userData = userDoc.data() as Map<String, dynamic>;

          // Count premium users
          if (userData['isPremium'] == true) {
            premiumUsers++;
          }

          // Count active users
          if (userData['lastLoginAt'] != null) {
            final lastLogin = (userData['lastLoginAt'] as Timestamp).toDate();
            if (lastLogin.isAfter(weekAgo)) {
              activeUsers++;
            }
          }

          // Count chats and characters
          final chatHistory = userData['chatHistory'] as List<dynamic>? ?? [];
          final userCharacters = userData['totalCharactersUsed'] as int? ?? 0;
          final dailyChats = Map<String, dynamic>.from(userData['dailyChats'] ?? {});

          totalChats += chatHistory.length;
          totalCharacters += userCharacters;

          // Get today's chats from dailyChats map for consistency
          todayChats += (dailyChats[today] ?? 0) as int;

          // Count weekly and monthly chats by time period
          for (var chat in chatHistory) {
            try {
              if (chat is Map<String, dynamic> && chat['timestamp'] != null) {
                DateTime? chatTime;

                // Handle different timestamp formats
                if (chat['timestamp'] is Timestamp) {
                  chatTime = (chat['timestamp'] as Timestamp).toDate();
                } else if (chat['timestamp'] is String) {
                  try {
                    chatTime = DateTime.parse(chat['timestamp']);
                  } catch (e) {
                    // Skip this chat if timestamp parsing fails
                    continue;
                  }
                }

                if (chatTime != null) {
                  if (chatTime.isAfter(weekStart)) weeklyChats++;
                  if (chatTime.isAfter(monthStart)) monthlyChats++;
                }
              }
            } catch (e) {
              debugPrint('Error processing chat timestamp: $e');
              // Continue processing other chats
            }
          }
        } catch (e) {
          debugPrint('Error processing user document ${userDoc.id}: $e');
          // Continue processing other users
        }
      }

      setState(() {
        _totalUsers = totalUsers;
        _premiumUsers = premiumUsers;
        _activeUsers = activeUsers;
        _totalChats = totalChats;
        _todayChats = todayChats;
        _weeklyChats = weeklyChats;
        _monthlyChats = monthlyChats;
        _totalCharacters = totalCharacters;
        _todayCharacters = todayCharacters;
        _weeklyCharacters = weeklyCharacters;
        _monthlyCharacters = monthlyCharacters;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error updating dashboard data: $e');
      // Set loading to false even if there's an error
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _updateRevenueData(QuerySnapshot snapshot) {
    if (!mounted) return;

    int totalRevenue = 0;
    for (var payment in snapshot.docs) {
      final paymentData = payment.data() as Map<String, dynamic>;
      final amount = paymentData['amount'] as int? ?? 0;
      totalRevenue += amount;
    }

    setState(() {
      _totalRevenue = totalRevenue;
    });
  }

  @override
  void dispose() {
    _usersSubscription.cancel();
    _paymentsSubscription.cancel();
    _pulseController.dispose();
    _rotationController.dispose();
    _slideController.dispose();
    _tabController.dispose();
    _apiKeyController.dispose();
    _apiUrlController.dispose();
    _notificationTitleController.dispose();
    _notificationMessageController.dispose();
    _notificationFrequencyController.dispose();
    _hyperlinkTextController.dispose();
    _hyperlinkUrlController.dispose();
    _bannerTextController.dispose();
    _bannerColorWordsController.dispose();
    _bannerHyperlinkTextController.dispose();
    _bannerHyperlinkUrlController.dispose();
    super.dispose();
  }



  Future<void> _loadApiConfiguration() async {
    // Load current API configuration (you can implement this based on your needs)
    _apiKeyController.text = "AIzaSyDXxWIwHQoYbs3Fl7Q4iq2r7hSVLhyMlBs";
    _apiUrlController.text = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";
  }

  Future<void> _saveApiConfiguration() async {
    try {
      // Save API configuration to Firestore admin collection
      await _firestore.collection('admin_config').doc('api_settings').set({
        'apiKey': _apiKeyController.text.trim(),
        'apiUrl': _apiUrlController.text.trim(),
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': _authService.currentUser?.email,
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('API কনফিগারেশন সফলভাবে সংরক্ষিত হয়েছে'),
            backgroundColor: AppColors.primaryGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('API কনফিগারেশন সংরক্ষণে ত্রুটি: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Format large numbers for display
  String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    }
    return number.toString();
  }

  Future<void> _signOut() async {
    try {
      await _authService.signOut();
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const AuthScreen()),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('সাইন আউট করতে সমস্যা হয়েছে: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DashboardColors.surfaceBg,
      body: _isLoading ? _buildLoadingScreen() : _buildModernDashboard(),
    );
  }

  Widget _buildModernDashboard() {
    return SafeArea(
      child: Column(
        children: [
          _buildModernAppBar(),
          Expanded(
            child: _buildTabbedDashboard(),
          ),
        ],
      ),
    );
  }

  Widget _buildModernAppBar() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            DashboardColors.primaryBlue.withOpacity(0.1),
            DashboardColors.primaryPurple.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(30),
          bottomRight: Radius.circular(30),
        ),
        boxShadow: [
          BoxShadow(
            color: DashboardColors.primaryBlue.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Row(
        children: [
          // Admin Avatar with Glow Effect
          AnimatedBuilder(
            animation: _pulseController,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: DashboardColors.primaryBlue.withOpacity(0.3 + _pulseController.value * 0.2),
                      blurRadius: 20 + _pulseController.value * 10,
                      spreadRadius: 2 + _pulseController.value * 3,
                    ),
                  ],
                ),
                child: CircleAvatar(
                  radius: 25,
                  backgroundColor: DashboardColors.primaryBlue,
                  child: const Icon(
                    Icons.admin_panel_settings,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 16),
          // Welcome Text with Animation
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AnimatedBuilder(
                  animation: _slideController,
                  builder: (context, child) {
                    return Transform.translate(
                      offset: Offset(0, -5 + _slideController.value * 10),
                      child: Text(
                        'স্বাগতম, ${_authService.currentUser?.email?.split('@')[0] ?? 'অ্যাডমিন'}!',
                        style: const TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: DashboardColors.textPrimary,
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 4),
                Text(
                  'UkilGiri Admin Control Center',
                  style: TextStyle(
                    fontSize: 14,
                    color: DashboardColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  DateFormat('EEEE, dd MMMM yyyy').format(DateTime.now()),
                  style: TextStyle(
                    fontSize: 12,
                    color: DashboardColors.textMuted,
                  ),
                ),
              ],
            ),
          ),
          // Action Buttons
          Row(
            children: [
              _buildActionButton(
                icon: Icons.notifications_active,
                color: DashboardColors.warning,
                onTap: () => _showNotifications(),
              ),
              const SizedBox(width: 12),
              _buildActionButton(
                icon: Icons.settings,
                color: DashboardColors.info,
                onTap: () => _tabController.animateTo(4),
              ),
              const SizedBox(width: 12),
              _buildActionButton(
                icon: Icons.logout,
                color: DashboardColors.error,
                onTap: _signOut,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedBuilder(
        animation: _pulseController,
        builder: (context, child) {
          return Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: color.withOpacity(0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: color.withOpacity(0.1 + _pulseController.value * 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          );
        },
      ),
    );
  }

  void _showNotifications() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: DashboardColors.cardBg,
        title: const Text(
          'সিস্টেম নোটিফিকেশন',
          style: TextStyle(color: DashboardColors.textPrimary),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildNotificationItem(
              'নতুন ব্যবহারকারী রেজিস্ট্রেশন',
              '৫ মিনিট আগে',
              DashboardColors.success,
              Icons.person_add,
            ),
            _buildNotificationItem(
              'পেমেন্ট সফল',
              '১০ মিনিট আগে',
              DashboardColors.info,
              Icons.payment,
            ),
            _buildNotificationItem(
              'সিস্টেম আপডেট',
              '১ ঘন্টা আগে',
              DashboardColors.warning,
              Icons.system_update,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'বন্ধ করুন',
              style: TextStyle(color: DashboardColors.primaryBlue),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationItem(String title, String time, Color color, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: DashboardColors.textPrimary,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
                Text(
                  time,
                  style: TextStyle(
                    color: DashboardColors.textMuted,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.sidebarBg,
      foregroundColor: AppColors.textPrimary,
      title: Row(
        children: [
          Icon(
            Icons.admin_panel_settings,
            color: AppColors.primaryGreen,
            size: 28,
          ),
          const SizedBox(width: 8),
          const Text(
            'অ্যাডমিন ড্যাশবোর্ড',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          onPressed: _signOut,
          icon: const Icon(Icons.logout),
          tooltip: 'সাইন আউট',
        ),
      ],
    );
  }

  Widget _buildLoadingScreen() {
    return const Center(
      child: EnhancedLoadingAnimation(
        message: 'ড্যাশবোর্ড লোড হচ্ছে...',
      ),
    );
  }

  Widget _buildTabbedDashboard() {
    return Column(
      children: [
        // Modern Tab Bar with Glass Morphism
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                DashboardColors.cardBg.withOpacity(0.8),
                DashboardColors.accentBg.withOpacity(0.6),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: DashboardColors.primaryBlue.withOpacity(0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: DashboardColors.primaryBlue.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: TabBar(
                controller: _tabController,
                isScrollable: true,
                labelColor: DashboardColors.primaryBlue,
                unselectedLabelColor: DashboardColors.textSecondary,
                indicatorColor: DashboardColors.primaryBlue,
                indicatorWeight: 3,
                indicatorPadding: const EdgeInsets.symmetric(horizontal: 8),
                labelStyle: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 13,
                ),
                unselectedLabelStyle: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                padding: const EdgeInsets.symmetric(vertical: 8),
                tabs: [
                  _buildModernTab(Icons.dashboard_rounded, 'ড্যাশবোর্ড', DashboardColors.primaryBlue),
                  _buildModernTab(Icons.people_rounded, 'ব্যবহারকারী', DashboardColors.primaryGreen),
                  _buildModernTab(Icons.chat_rounded, 'চ্যাট', DashboardColors.primaryPurple),
                  _buildModernTab(Icons.payment_rounded, 'পেমেন্ট', DashboardColors.primaryOrange),
                  _buildModernTab(Icons.settings_rounded, 'সেটিংস', DashboardColors.primaryTeal),
                ],
              ),
            ),
          ),
        ),
        // Tab Content with Smooth Transitions
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildUsersTab(),
              _buildChatsTab(),
              _buildPaymentsTab(),
              _buildSettingsTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildModernTab(IconData icon, String text, Color color) {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Tab(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: color.withOpacity(0.1 + _pulseController.value * 0.05),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 18,
                  color: color,
                ),
                const SizedBox(width: 6),
                Text(
                  text,
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Overview Tab - Main Dashboard
  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildEnhancedStatsGrid(),
          const SizedBox(height: 20),
          _buildRealtimeCharts(),
          const SizedBox(height: 20),
          _buildQuickActions(),
        ],
      ),
    );
  }



  Widget _buildEnhancedStatsGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header with Animation
        AnimatedBuilder(
          animation: _slideController,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(-20 + _slideController.value * 40, 0),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          DashboardColors.primaryBlue,
                          DashboardColors.primaryPurple,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.analytics_rounded,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'রিয়েল-টাইম অ্যানালিটিক্স',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: DashboardColors.textPrimary,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        const SizedBox(height: 20),
        // Modern Stats Grid
        LayoutBuilder(
          builder: (context, constraints) {
            int crossAxisCount = constraints.maxWidth > 800 ? 5 : (constraints.maxWidth > 600 ? 3 : 2);
            double childAspectRatio = constraints.maxWidth > 800 ? 1.2 : (constraints.maxWidth > 600 ? 1.1 : 0.85);

            return GridView.count(
              crossAxisCount: crossAxisCount,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: childAspectRatio,
              children: [
                _buildModernStatCard(
                  title: 'মোট ব্যবহারকারী',
                  value: _totalUsers.toString(),
                  subtitle: 'সক্রিয়: $_activeUsers',
                  icon: Icons.people_rounded,
                  gradient: [DashboardColors.primaryGreen, DashboardColors.primaryTeal],
                  trend: '+12%',
                  isPositive: true,
                ),
                _buildModernStatCard(
                  title: 'মোট চ্যাট',
                  value: _totalChats.toString(),
                  subtitle: 'আজ: $_todayChats',
                  icon: Icons.chat_rounded,
                  gradient: [DashboardColors.primaryPurple, DashboardColors.primaryPink],
                  trend: '+8%',
                  isPositive: true,
                ),
                _buildModernStatCard(
                  title: 'মোট অক্ষর',
                  value: _formatNumber(_totalCharacters),
                  subtitle: 'AI রেসপন্স',
                  icon: Icons.text_fields_rounded,
                  gradient: [DashboardColors.primaryBlue, DashboardColors.primaryIndigo],
                  trend: '+20%',
                  isPositive: true,
                ),
                _buildModernStatCard(
                  title: 'প্রিমিয়াম ব্যবহারকারী',
                  value: _premiumUsers.toString(),
                  subtitle: 'রেভিনিউ: ৳$_totalRevenue',
                  icon: Icons.star_rounded,
                  gradient: [DashboardColors.primaryOrange, DashboardColors.warning],
                  trend: '+25%',
                  isPositive: true,
                ),
                _buildModernStatCard(
                  title: 'সাপ্তাহিক চ্যাট',
                  value: _weeklyChats.toString(),
                  subtitle: 'মাসিক: $_monthlyChats',
                  icon: Icons.trending_up_rounded,
                  gradient: [DashboardColors.primaryTeal, DashboardColors.primaryGreen],
                  trend: '+15%',
                  isPositive: true,
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildModernStatCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required List<Color> gradient,
    required String trend,
    required bool isPositive,
  }) {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 + _pulseController.value * 0.03,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  DashboardColors.cardBg,
                  DashboardColors.accentBg.withOpacity(0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: gradient[0].withOpacity(0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: gradient[0].withOpacity(0.2),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
                BoxShadow(
                  color: gradient[1].withOpacity(0.1),
                  blurRadius: 40,
                  offset: const Offset(0, 16),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        gradient[0].withOpacity(0.1),
                        gradient[1].withOpacity(0.05),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header Row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(colors: gradient),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: gradient[0].withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Icon(
                              icon,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          // Trend Indicator
                          AnimatedBuilder(
                            animation: _rotationController,
                            builder: (context, child) {
                              return Transform.rotate(
                                angle: _rotationController.value * 0.1,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: isPositive
                                        ? DashboardColors.success.withOpacity(0.2)
                                        : DashboardColors.error.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: isPositive
                                          ? DashboardColors.success.withOpacity(0.5)
                                          : DashboardColors.error.withOpacity(0.5),
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        isPositive ? Icons.trending_up : Icons.trending_down,
                                        color: isPositive ? DashboardColors.success : DashboardColors.error,
                                        size: 12,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        trend,
                                        style: TextStyle(
                                          fontSize: 10,
                                          color: isPositive ? DashboardColors.success : DashboardColors.error,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      // Value
                      Flexible(
                        child: AnimatedBuilder(
                          animation: _slideController,
                          builder: (context, child) {
                            return Transform.translate(
                              offset: Offset(0, -2 + _slideController.value * 4),
                              child: Text(
                                value,
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: DashboardColors.textPrimary,
                                  shadows: [
                                    Shadow(
                                      color: gradient[0].withOpacity(0.3),
                                      blurRadius: 6,
                                      offset: const Offset(0, 1),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 6),
                      // Title
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 12,
                          color: DashboardColors.textSecondary,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      // Subtitle
                      Text(
                        subtitle,
                        style: const TextStyle(
                          fontSize: 10,
                          color: DashboardColors.textMuted,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // Real-time Charts Section
  Widget _buildRealtimeCharts() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'রিয়েল-টাইম চার্ট',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          height: 200,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppColors.primaryGreen.withOpacity(0.1),
                AppColors.primaryPurple.withOpacity(0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.primaryGreen.withOpacity(0.3)),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildMiniChart('দৈনিক চ্যাট', _todayChats, Colors.blue),
                  _buildMiniChart('সাপ্তাহিক', _weeklyChats, Colors.green),
                  _buildMiniChart('মাসিক', _monthlyChats, Colors.orange),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: AnimatedBuilder(
                  animation: _slideController,
                  builder: (context, child) {
                    return Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: AppColors.sidebarBg.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Center(
                        child: Text(
                          'চ্যাট ট্রেন্ড গ্রাফ\n(শীঘ্রই আসছে)',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: AppColors.textPrimary,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMiniChart(String title, int value, Color color) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: color.withOpacity(0.3)),
          ),
          child: Center(
            child: Text(
              value.toString(),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: TextStyle(
            fontSize: 10,
            color: AppColors.textPrimary.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  // Quick Actions Section
  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'দ্রুত কার্যক্রম',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 2.5,
          children: [
            _buildQuickActionCard(
              'ব্যবহারকারী পরিচালনা',
              Icons.people_outline,
              AppColors.primaryGreen,
              () => _tabController.animateTo(1),
            ),
            _buildQuickActionCard(
              'চ্যাট মনিটরিং',
              Icons.chat_outlined,
              AppColors.primaryPurple,
              () => _tabController.animateTo(2),
            ),
            _buildQuickActionCard(
              'পেমেন্ট রিপোর্ট',
              Icons.payment,
              Colors.orange,
              () => _tabController.animateTo(3),
            ),
            _buildQuickActionCard(
              'সিস্টেম সেটিংস',
              Icons.settings,
              Colors.blue,
              () => _tabController.animateTo(4),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedBuilder(
        animation: _pulseController,
        builder: (context, child) {
          return Transform.scale(
            scale: 1.0 + _pulseController.value * 0.01,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    color.withOpacity(0.1),
                    color.withOpacity(0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: color.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildApiConfigurationSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.withOpacity(0.1),
            Colors.purple.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.blue.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.api,
                color: Colors.blue,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'API কনফিগারেশন',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _apiKeyController,
            decoration: InputDecoration(
              labelText: 'API Key',
              hintText: 'Gemini API Key লিখুন',
              prefixIcon: const Icon(Icons.key, size: 20),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              filled: true,
              fillColor: Colors.white.withOpacity(0.1),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            ),
            style: const TextStyle(color: AppColors.textPrimary, fontSize: 14),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _apiUrlController,
            decoration: InputDecoration(
              labelText: 'API URL',
              hintText: 'Gemini API URL লিখুন',
              prefixIcon: const Icon(Icons.link, size: 20),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              filled: true,
              fillColor: Colors.white.withOpacity(0.1),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            ),
            style: const TextStyle(color: AppColors.textPrimary, fontSize: 14),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _saveApiConfiguration,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              icon: const Icon(Icons.save, size: 18),
              label: const Text(
                'কনফিগারেশন সংরক্ষণ করুন',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }



  // Users Tab - Professional User Management Interface
  Widget _buildUsersTab() {
    return Column(
      children: [
        // Enhanced Header Section
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
          child: _buildUsersHeader(),
        ),

        // Users List
        Expanded(
          child: StreamBuilder<QuerySnapshot>(
            stream: _usersStream,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return _buildLoadingState();
              }

              if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                return _buildEmptyState();
              }

              return _buildUsersList(snapshot.data!.docs);
            },
          ),
        ),
      ],
    );
  }

  // Enhanced Header with Search and Filters
  Widget _buildUsersHeader() {
    return Container(
      height: 140, // Fixed height to prevent overflow
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            DashboardColors.primaryBlue.withOpacity(0.1),
            DashboardColors.primaryPurple.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: Border(
          bottom: BorderSide(
            color: DashboardColors.primaryBlue.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title Section
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [DashboardColors.primaryBlue, DashboardColors.primaryPurple],
                    ),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: const Icon(
                    Icons.people_rounded,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'ব্যবহারকারী পরিচালনা',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 6),
            const Text(
              'সকল ব্যবহারকারীর বিস্তারিত তথ্য এবং কার্যকলাপ',
              style: TextStyle(
                fontSize: 10,
                color: AppColors.textSecondary,
              ),
            ),

            const SizedBox(height: 8),

            // Quick Stats Row
            _buildCompactStatsCard(),

            const SizedBox(height: 8),

            // Search Bar
            Container(
              height: 32,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'ব্যবহারকারী খুঁজুন',
                  hintStyle: const TextStyle(fontSize: 12),
                  prefixIcon: const Icon(Icons.search, color: DashboardColors.primaryBlue, size: 18),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
              ),
            ),

            const SizedBox(height: 6),

            // Filter Buttons Row
            Row(
              children: [
                _buildCompactFilterButton('সকল', true),
                const SizedBox(width: 6),
                _buildCompactFilterButton('প্রিমিয়াম', false),
                const SizedBox(width: 6),
                _buildCompactFilterButton('সক্রিয়', false),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Compact Stats Card
  Widget _buildCompactStatsCard() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(child: _buildCompactMiniStat('মোট', _totalUsers.toString(), DashboardColors.primaryBlue)),
          const SizedBox(width: 6),
          Expanded(child: _buildCompactMiniStat('প্রিমিয়াম', _premiumUsers.toString(), DashboardColors.primaryOrange)),
          const SizedBox(width: 6),
          Expanded(child: _buildCompactMiniStat('সক্রিয়', _activeUsers.toString(), DashboardColors.primaryGreen)),
        ],
      ),
    );
  }

  // Compact Mini Stat Widget
  Widget _buildCompactMiniStat(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 7,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  // Compact Filter Button
  Widget _buildCompactFilterButton(String text, bool isActive) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        gradient: isActive
          ? const LinearGradient(colors: [DashboardColors.primaryBlue, DashboardColors.primaryPurple])
          : null,
        color: isActive ? null : Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isActive ? Colors.transparent : DashboardColors.primaryBlue.withOpacity(0.3),
        ),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: isActive ? Colors.white : DashboardColors.primaryBlue,
          fontWeight: FontWeight.w600,
          fontSize: 10,
        ),
      ),
    );
  }

  // Mini Stat Widget
  Widget _buildMiniStat(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  // Filter Button
  Widget _buildFilterButton(String text, bool isActive) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: isActive
          ? LinearGradient(colors: [DashboardColors.primaryBlue, DashboardColors.primaryPurple])
          : null,
        color: isActive ? null : Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isActive ? Colors.transparent : DashboardColors.primaryBlue.withOpacity(0.3),
        ),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: isActive ? Colors.white : DashboardColors.primaryBlue,
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
    );
  }

  // Loading State
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.9),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(DashboardColors.primaryBlue),
                ),
                const SizedBox(height: 16),
                Text(
                  'ব্যবহারকারীর তথ্য লোড হচ্ছে...',
                  style: TextStyle(
                    color: AppColors.textPrimary,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Empty State
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(40),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.9),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [DashboardColors.primaryBlue.withOpacity(0.2), DashboardColors.primaryPurple.withOpacity(0.2)],
                    ),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.people_outline,
                    size: 60,
                    color: DashboardColors.primaryBlue,
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  'কোন ব্যবহারকারী পাওয়া যায়নি',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'এখনও কোন ব্যবহারকারী নিবন্ধন করেননি',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Professional Users List
  Widget _buildUsersList(List<QueryDocumentSnapshot> users) {
    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 20),
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        final userData = user.data() as Map<String, dynamic>;
        return _buildProfessionalUserCard(user.id, userData);
      },
    );
  }

  // Professional User Card Design
  Widget _buildProfessionalUserCard(String userId, Map<String, dynamic> userData) {
    final chatHistory = userData['chatHistory'] as List<dynamic>? ?? [];
    final charactersUsed = userData['totalCharactersUsed'] as int? ?? 0;
    final isPremium = userData['isPremium'] == true;
    final joinDate = userData['createdAt'] != null
        ? (userData['createdAt'] as Timestamp).toDate()
        : DateTime.now();
    final lastLogin = userData['lastLoginAt'] != null
        ? (userData['lastLoginAt'] as Timestamp).toDate()
        : null;

    // Get today's chats from dailyChats map for consistency
    final now = DateTime.now();
    String today = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
    Map<String, dynamic> dailyChats = Map<String, dynamic>.from(userData['dailyChats'] ?? {});
    int todayChats = dailyChats[today] ?? 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      constraints: const BoxConstraints(maxWidth: double.infinity),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white,
            Colors.white.withOpacity(0.95),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: isPremium
            ? DashboardColors.primaryOrange.withOpacity(0.3)
            : DashboardColors.primaryBlue.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Header Section
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isPremium
                  ? [DashboardColors.primaryOrange.withOpacity(0.1), DashboardColors.primaryOrange.withOpacity(0.05)]
                  : [DashboardColors.primaryBlue.withOpacity(0.1), DashboardColors.primaryPurple.withOpacity(0.05)],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                // User Avatar
                _buildEnhancedAvatar(userData),

                const SizedBox(width: 12),

                // User Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              userData['name'] ?? 'Unknown User',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textPrimary,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (isPremium) _buildPremiumBadge(),
                        ],
                      ),
                      const SizedBox(height: 2),
                      Text(
                        userData['email'] ?? 'No email',
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      _buildUserStatusRow(lastLogin),
                    ],
                  ),
                ),

                // Action Menu
                _buildActionMenu(userId, userData),
              ],
            ),
          ),

          // Stats Section
          Container(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                // Primary Stats Row
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'মোট চ্যাট',
                        chatHistory.length.toString(),
                        Icons.chat_bubble_outline,
                        DashboardColors.primaryGreen,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatCard(
                        'আজকের চ্যাট',
                        todayChats.toString(),
                        Icons.today,
                        DashboardColors.primaryBlue,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatCard(
                        'মোট অক্ষর',
                        _formatNumber(charactersUsed),
                        Icons.text_fields,
                        DashboardColors.primaryPurple,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Secondary Stats Row
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoCard(
                        'যোগদানের তারিখ',
                        DateFormat('dd MMM yyyy').format(joinDate),
                        Icons.calendar_today,
                        DashboardColors.info,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildInfoCard(
                        'সর্বশেষ লগইন',
                        lastLogin != null
                          ? _getTimeAgo(lastLogin)
                          : 'কখনো নয়',
                        Icons.access_time,
                        DashboardColors.warning,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Enhanced Avatar with Profile Picture Support
  Widget _buildEnhancedAvatar(Map<String, dynamic> userData) {
    final name = userData['name'] ?? 'User';
    final profileImageUrl = userData['profileImageUrl'] as String?;

    return Container(
      width: 45,
      height: 45,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          colors: [
            DashboardColors.primaryBlue,
            DashboardColors.primaryPurple,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: DashboardColors.primaryBlue.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: profileImageUrl != null && profileImageUrl.isNotEmpty
        ? ClipOval(
            child: Image.network(
              profileImageUrl,
              width: 45,
              height: 45,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return _buildInitialsAvatar(name);
              },
            ),
          )
        : _buildInitialsAvatar(name),
    );
  }

  // Initials Avatar
  Widget _buildInitialsAvatar(String name) {
    String initials = '';
    List<String> nameParts = name.split(' ');
    if (nameParts.isNotEmpty) {
      initials = nameParts[0].isNotEmpty ? nameParts[0][0].toUpperCase() : 'U';
      if (nameParts.length > 1 && nameParts[1].isNotEmpty) {
        initials += nameParts[1][0].toUpperCase();
      }
    }
    if (initials.isEmpty) initials = 'U';

    return Center(
      child: Text(
        initials,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  // Premium Badge
  Widget _buildPremiumBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [DashboardColors.primaryOrange, Colors.amber],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: DashboardColors.primaryOrange.withOpacity(0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.star,
            color: Colors.white,
            size: 14,
          ),
          const SizedBox(width: 4),
          Text(
            'প্রিমিয়াম',
            style: TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  // User Status Row
  Widget _buildUserStatusRow(DateTime? lastLogin) {
    final isOnline = lastLogin != null &&
        DateTime.now().difference(lastLogin).inMinutes < 30;

    return Row(
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: isOnline ? DashboardColors.primaryGreen : Colors.grey,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 6),
        Text(
          isOnline ? 'অনলাইন' : 'অফলাইন',
          style: TextStyle(
            fontSize: 12,
            color: isOnline ? DashboardColors.primaryGreen : Colors.grey,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 12),
        Icon(
          Icons.access_time,
          size: 12,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Text(
          lastLogin != null ? _getTimeAgo(lastLogin) : 'কখনো নয়',
          style: TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  // Action Menu
  Widget _buildActionMenu(String userId, Map<String, dynamic> userData) {
    return PopupMenuButton<String>(
      icon: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.8),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: DashboardColors.primaryBlue.withOpacity(0.2),
          ),
        ),
        child: Icon(
          Icons.more_vert,
          color: DashboardColors.primaryBlue,
          size: 18,
        ),
      ),
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'view',
          child: Row(
            children: [
              Icon(Icons.visibility, size: 16, color: DashboardColors.info),
              const SizedBox(width: 8),
              Text('বিস্তারিত দেখুন'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'premium',
          child: Row(
            children: [
              Icon(
                userData['isPremium'] == true ? Icons.star_border : Icons.star,
                size: 16,
                color: DashboardColors.primaryOrange,
              ),
              const SizedBox(width: 8),
              Text(userData['isPremium'] == true ? 'প্রিমিয়াম বাতিল' : 'প্রিমিয়াম করুন'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'message',
          child: Row(
            children: [
              Icon(Icons.message, size: 16, color: DashboardColors.primaryGreen),
              const SizedBox(width: 8),
              Text('বার্তা পাঠান'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 16, color: DashboardColors.error),
              const SizedBox(width: 8),
              Text('মুছে ফেলুন'),
            ],
          ),
        ),
      ],
      onSelected: (value) => _handleUserAction(value, userId, userData),
    );
  }

  // Stat Card for User Stats
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withOpacity(0.1),
            color.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              fontSize: 8,
              color: AppColors.textPrimary.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  // Info Card for Secondary Information
  Widget _buildInfoCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 14),
          const SizedBox(width: 6),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 8,
                    color: AppColors.textSecondary,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Time Ago Helper
  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} দিন আগে';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ঘন্টা আগে';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} মিনিট আগে';
    } else {
      return 'এখনই';
    }
  }

  // Handle User Actions
  void _handleUserAction(String action, String userId, Map<String, dynamic> userData) {
    switch (action) {
      case 'view':
        _showUserDetails(userId, userData);
        break;
      case 'premium':
        _togglePremiumStatus(userId, userData);
        break;
      case 'message':
        _sendMessageToUser(userId, userData);
        break;
      case 'delete':
        _deleteUser(userId, userData);
        break;
    }
  }

  // Show User Details Dialog
  void _showUserDetails(String userId, Map<String, dynamic> userData) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: DashboardColors.cardBg,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.person, color: DashboardColors.primaryBlue),
            const SizedBox(width: 8),
            Text(
              'ব্যবহারকারীর বিস্তারিত',
              style: TextStyle(color: AppColors.textPrimary),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('নাম', userData['name'] ?? 'Unknown'),
              _buildDetailRow('ইমেইল', userData['email'] ?? 'No email'),
              _buildDetailRow('ফোন', userData['phone'] ?? 'No phone'),
              _buildDetailRow('স্ট্যাটাস', userData['isPremium'] == true ? 'প্রিমিয়াম' : 'ফ্রি'),
              _buildDetailRow('মোট চ্যাট', '${(userData['chatHistory'] as List?)?.length ?? 0}'),
              _buildDetailRow('মোট অক্ষর', '${userData['totalCharactersUsed'] ?? 0}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('বন্ধ করুন', style: TextStyle(color: DashboardColors.primaryBlue)),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: AppColors.textPrimary),
            ),
          ),
        ],
      ),
    );
  }

  // Toggle Premium Status
  void _togglePremiumStatus(String userId, Map<String, dynamic> userData) {
    final isPremium = userData['isPremium'] == true;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: DashboardColors.cardBg,
        title: Text(
          isPremium ? 'প্রিমিয়াম বাতিল করুন' : 'প্রিমিয়াম করুন',
          style: TextStyle(color: AppColors.textPrimary),
        ),
        content: Text(
          isPremium
            ? 'এই ব্যবহারকারীর প্রিমিয়াম স্ট্যাটাস বাতিল করতে চান?'
            : 'এই ব্যবহারকারীকে প্রিমিয়াম করতে চান?',
          style: TextStyle(color: AppColors.textPrimary),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('বাতিল'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                await _firestore.collection('users').doc(userId).update({
                  'isPremium': !isPremium,
                });
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(isPremium ? 'প্রিমিয়াম বাতিল করা হয়েছে' : 'প্রিমিয়াম করা হয়েছে'),
                    backgroundColor: DashboardColors.primaryGreen,
                  ),
                );
              } catch (e) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('ত্রুটি: $e'),
                    backgroundColor: DashboardColors.error,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: isPremium ? DashboardColors.error : DashboardColors.primaryOrange,
            ),
            child: Text(isPremium ? 'বাতিল করুন' : 'প্রিমিয়াম করুন'),
          ),
        ],
      ),
    );
  }

  // Send Message to User (Placeholder)
  void _sendMessageToUser(String userId, Map<String, dynamic> userData) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('বার্তা পাঠানোর ফিচার শীঘ্রই আসছে'),
        backgroundColor: DashboardColors.info,
      ),
    );
  }

  // Delete User
  void _deleteUser(String userId, Map<String, dynamic> userData) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: DashboardColors.cardBg,
        title: Text(
          'ব্যবহারকারী মুছুন',
          style: TextStyle(color: DashboardColors.error),
        ),
        content: Text(
          'আপনি কি নিশ্চিত যে এই ব্যবহারকারীকে মুছে ফেলতে চান? এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।',
          style: TextStyle(color: AppColors.textPrimary),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('বাতিল'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                await _firestore.collection('users').doc(userId).delete();
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('ব্যবহারকারী মুছে ফেলা হয়েছে'),
                    backgroundColor: DashboardColors.error,
                  ),
                );
              } catch (e) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('ত্রুটি: $e'),
                    backgroundColor: DashboardColors.error,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: DashboardColors.error),
            child: Text('মুছে ফেলুন'),
          ),
        ],
      ),
    );
  }

  // Chats Tab - Chat Monitoring
  Widget _buildChatsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'চ্যাট মনিটরিং',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          // Chat Statistics Row
          Row(
            children: [
              Expanded(
                child: _buildChatStatCard(
                  'আজকের চ্যাট',
                  _todayChats.toString(),
                  Icons.today,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildChatStatCard(
                  'সাপ্তাহিক চ্যাট',
                  _weeklyChats.toString(),
                  Icons.date_range,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Character Statistics Row
          Row(
            children: [
              Expanded(
                child: _buildChatStatCard(
                  'মোট অক্ষর',
                  _formatNumber(_totalCharacters),
                  Icons.text_fields,
                  Colors.purple,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildChatStatCard(
                  'গড় অক্ষর/চ্যাট',
                  _totalChats > 0 ? _formatNumber((_totalCharacters / _totalChats).round()) : '0',
                  Icons.analytics,
                  Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.sidebarBg.withOpacity(0.5),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.primaryGreen.withOpacity(0.3)),
            ),
            child: const Center(
              child: Text(
                'রিয়েল-টাইম চ্যাট মনিটরিং\n(শীঘ্রই আসছে)',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: AppColors.textPrimary,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChatStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withOpacity(0.1),
            color.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textPrimary.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }



  // Payments Tab - Payment Management
  Widget _buildPaymentsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'পেমেন্ট পরিচালনা',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.green.withOpacity(0.1),
                  Colors.blue.withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                const Icon(Icons.monetization_on, color: Colors.green, size: 32),
                const SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'মোট রেভিনিউ',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      '৳$_totalRevenue',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          StreamBuilder<QuerySnapshot>(
            stream: _paymentsStream,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                return const Center(
                  child: Text(
                    'কোন পেমেন্ট রেকর্ড পাওয়া যায়নি',
                    style: TextStyle(color: AppColors.textPrimary),
                  ),
                );
              }

              return ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: snapshot.data!.docs.length,
                itemBuilder: (context, index) {
                  final payment = snapshot.data!.docs[index];
                  final paymentData = payment.data() as Map<String, dynamic>;

                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: const CircleAvatar(
                        backgroundColor: Colors.green,
                        child: Icon(Icons.payment, color: Colors.white),
                      ),
                      title: Text('৳${paymentData['amount'] ?? 0}'),
                      subtitle: Text(paymentData['userEmail'] ?? 'Unknown'),
                      trailing: Text(
                        DateFormat('dd/MM/yyyy').format(
                          (paymentData['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
                        ),
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  // Settings Tab - System Settings
  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'সিস্টেম সেটিংস',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildBannerTextEditorSection(),
          const SizedBox(height: 20),
          _buildFeedbackManagementSection(),
          const SizedBox(height: 20),
          _buildDeveloperManagementSection(),
          const SizedBox(height: 20),
          _buildNotificationManagementSection(),
          const SizedBox(height: 20),
          _buildApiConfigurationSection(),
          const SizedBox(height: 20),
          _buildSystemControlsSection(),
        ],
      ),
    );
  }

  // Notification Management Section
  Widget _buildNotificationManagementSection() {
    return StreamBuilder<DocumentSnapshot>(
      stream: _firestore.collection('admin_settings').doc('notifications').snapshots(),
      builder: (context, snapshot) {
        Map<String, dynamic> notificationData = {};

        if (snapshot.hasData && snapshot.data!.exists) {
          notificationData = snapshot.data!.data() as Map<String, dynamic>;

          // Update controllers with current data only if they're empty
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (_notificationTitleController.text.isEmpty) {
              _notificationTitleController.text = notificationData['title'] ?? '';
            }
            if (_notificationMessageController.text.isEmpty) {
              _notificationMessageController.text = notificationData['message'] ?? '';
            }
            if (_notificationFrequencyController.text.isEmpty) {
              _notificationFrequencyController.text = notificationData['frequency']?.toString() ?? '1';
            }
            if (_hyperlinkTextController.text.isEmpty) {
              _hyperlinkTextController.text = notificationData['hyperlinkText'] ?? '';
            }
            if (_hyperlinkUrlController.text.isEmpty) {
              _hyperlinkUrlController.text = notificationData['hyperlinkUrl'] ?? '';
            }
            // Don't update _isNotificationEnabled here since it's loaded in initState
          });
        }

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                DashboardColors.primaryPurple.withOpacity(0.1),
                DashboardColors.primaryBlue.withOpacity(0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: DashboardColors.primaryPurple.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.notifications_active, color: DashboardColors.primaryPurple, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'পপআপ নোটিফিকেশন ম্যানেজমেন্ট',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Switch(
                    value: _isNotificationEnabled,
                    onChanged: (value) {
                      setState(() {
                        _isNotificationEnabled = value;
                      });
                      _saveNotificationSettings();
                    },
                    activeColor: DashboardColors.primaryGreen,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _buildNotificationForm(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSystemControlsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.red.withOpacity(0.1),
            Colors.orange.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.security, color: Colors.red, size: 20),
              SizedBox(width: 8),
              Text(
                'সিস্টেম নিয়ন্ত্রণ',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 2.5,
            children: [
              _buildSystemControlCard(
                'ডাটাবেস ব্যাকআপ',
                Icons.backup,
                Colors.blue,
                () {
                  // Implement database backup
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('ব্যাকআপ শুরু হয়েছে...')),
                  );
                },
              ),
              _buildSystemControlCard(
                'ক্যাশ ক্লিয়ার',
                Icons.clear_all,
                Colors.orange,
                () {
                  // Implement cache clear
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('ক্যাশ ক্লিয়ার করা হয়েছে')),
                  );
                },
              ),
              _buildSystemControlCard(
                'সিস্টেম রিস্টার্ট',
                Icons.restart_alt,
                Colors.red,
                () {
                  // Show confirmation dialog
                  _showRestartConfirmation();
                },
              ),
              _buildSystemControlCard(
                'লগ এক্সপোর্ট',
                Icons.file_download,
                Colors.green,
                () {
                  // Implement log export
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('লগ এক্সপোর্ট করা হচ্ছে...')),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSystemControlCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              color.withOpacity(0.1),
              color.withOpacity(0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 16,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationForm() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildNotificationTextField(
                controller: _notificationTitleController,
                label: 'পপআপ টাইটেল',
                hint: 'যেমন: গুরুত্বপূর্ণ ঘোষণা',
                icon: Icons.title,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildNotificationTextField(
                controller: _notificationFrequencyController,
                label: 'দৈনিক ফ্রিকোয়েন্সি',
                hint: 'যেমন: 1',
                icon: Icons.repeat,
                keyboardType: TextInputType.number,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _buildNotificationTextField(
          controller: _notificationMessageController,
          label: 'পপআপ মেসেজ',
          hint: 'আপনার বার্তা এখানে লিখুন...',
          icon: Icons.message,
          maxLines: 3,
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildNotificationTextField(
                controller: _hyperlinkTextController,
                label: 'হাইপারলিংক টেক্সট',
                hint: 'যেমন: আরও জানুন',
                icon: Icons.link,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildNotificationTextField(
                controller: _hyperlinkUrlController,
                label: 'হাইপারলিংক URL',
                hint: 'https://example.com',
                icon: Icons.web,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _saveNotificationSettings,
                icon: const Icon(Icons.save, size: 18),
                label: const Text('সেভ করুন'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: DashboardColors.primaryGreen,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _testNotification,
                icon: const Icon(Icons.preview, size: 18),
                label: const Text('টেস্ট করুন'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: DashboardColors.primaryBlue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNotificationTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 4),
        TextField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          style: const TextStyle(fontSize: 14),
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, size: 18),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: DashboardColors.primaryPurple),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            filled: true,
            fillColor: Colors.white,
          ),
        ),
      ],
    );
  }

  // Load notification settings from Firestore
  Future<void> _loadNotificationSettings() async {
    try {
      debugPrint('Loading notification settings...');
      final doc = await _firestore.collection('admin_settings').doc('notifications').get();

      if (doc.exists && mounted) {
        final data = doc.data() as Map<String, dynamic>;
        setState(() {
          _isNotificationEnabled = data['enabled'] ?? false;
          _notificationTitleController.text = data['title'] ?? '';
          _notificationMessageController.text = data['message'] ?? '';
          _notificationFrequencyController.text = (data['frequency'] ?? 1).toString();
          _hyperlinkTextController.text = data['hyperlinkText'] ?? '';
          _hyperlinkUrlController.text = data['hyperlinkUrl'] ?? '';
        });
        debugPrint('Notification settings loaded: enabled = $_isNotificationEnabled');
      } else {
        debugPrint('No notification settings found, using defaults');
      }
    } catch (e) {
      debugPrint('Error loading notification settings: $e');
    }
  }

  Future<void> _saveNotificationSettings() async {
    try {
      await _firestore.collection('admin_settings').doc('notifications').set({
        'enabled': _isNotificationEnabled,
        'title': _notificationTitleController.text.trim(),
        'message': _notificationMessageController.text.trim(),
        'frequency': int.tryParse(_notificationFrequencyController.text.trim()) ?? 1,
        'hyperlinkText': _hyperlinkTextController.text.trim(),
        'hyperlinkUrl': _hyperlinkUrlController.text.trim(),
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': _authService.currentUser?.email ?? 'admin',
      }, SetOptions(merge: true));

      debugPrint('Notification settings saved successfully: enabled = $_isNotificationEnabled');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('নোটিফিকেশন সেটিংস সফলভাবে সেভ হয়েছে'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('সেভ করতে সমস্যা হয়েছে: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _testNotification() {
    if (_notificationTitleController.text.trim().isEmpty ||
        _notificationMessageController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('টাইটেল এবং মেসেজ ফিল্ড পূরণ করুন'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    _showTestNotificationDialog();
  }

  void _showTestNotificationDialog() {
    showDialog(
      context: context,
      builder: (context) => _buildNotificationDialog(
        title: _notificationTitleController.text.trim(),
        message: _notificationMessageController.text.trim(),
        hyperlinkText: _hyperlinkTextController.text.trim(),
        hyperlinkUrl: _hyperlinkUrlController.text.trim(),
        isTest: true,
      ),
    );
  }

  Widget _buildNotificationDialog({
    required String title,
    required String message,
    required String hyperlinkText,
    required String hyperlinkUrl,
    bool isTest = false,
  }) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        margin: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              DashboardColors.primaryBlue.withOpacity(0.95),
              DashboardColors.primaryPurple.withOpacity(0.95),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with icon
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.notifications_active,
                  color: Colors.white,
                  size: 32,
                ),
              ),
              const SizedBox(height: 16),

              // Title
              Text(
                title.isNotEmpty ? title : 'নোটিফিকেশন',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),

              // Message
              Text(
                message.isNotEmpty ? message : 'আপনার বার্তা এখানে দেখানো হবে',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),

              // Action buttons
              Row(
                children: [
                  if (hyperlinkText.isNotEmpty && hyperlinkUrl.isNotEmpty)
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          if (!isTest) {
                            // In real implementation, open URL
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('Opening: $hyperlinkUrl')),
                            );
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: DashboardColors.primaryBlue,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                        child: Text(hyperlinkText),
                      ),
                    ),
                  if (hyperlinkText.isNotEmpty && hyperlinkUrl.isNotEmpty)
                    const SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.white,
                        side: const BorderSide(color: Colors.white, width: 2),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                      ),
                      child: Text(isTest ? 'বন্ধ করুন' : 'ঠিক আছে'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showRestartConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('সিস্টেম রিস্টার্ট'),
        content: const Text('আপনি কি নিশ্চিত যে সিস্টেম রিস্টার্ট করতে চান?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('বাতিল'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('সিস্টেম রিস্টার্ট করা হচ্ছে...')),
              );
            },
            child: const Text('রিস্টার্ট'),
          ),
        ],
      ),
    );
  }

  // Banner Text Editor Section
  Widget _buildBannerTextEditorSection() {
    return StreamBuilder<DocumentSnapshot>(
      stream: _firestore.collection('admin_settings').doc('banner_text').snapshots(),
      builder: (context, snapshot) {
        Map<String, dynamic> bannerData = {};

        if (snapshot.hasData && snapshot.data!.exists) {
          bannerData = snapshot.data!.data() as Map<String, dynamic>;

          // Update controllers with current data only if they're empty
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (_bannerTextController.text.isEmpty) {
              _bannerTextController.text = bannerData['text'] ?? 'এই আপ্লিকেশনটি বানিয়েছেন- সালাউদ্দিন মজুমদার। ©All rights reserved • UkilGiri - উকিলগিরি • আইনি সহায়তার নতুন দিগন্ত • ';
            }
            if (_bannerColorWordsController.text.isEmpty) {
              _bannerColorWordsController.text = bannerData['colorWords'] ?? '';
            }
            if (_bannerHyperlinkTextController.text.isEmpty) {
              _bannerHyperlinkTextController.text = bannerData['hyperlinkText'] ?? '';
            }
            if (_bannerHyperlinkUrlController.text.isEmpty) {
              _bannerHyperlinkUrlController.text = bannerData['hyperlinkUrl'] ?? '';
            }
            if (_bannerScrollSpeedController.text.isEmpty) {
              _bannerScrollSpeedController.text = (bannerData['scrollSpeed'] ?? 20).toString();
            }
          });
        }

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                DashboardColors.primaryOrange.withOpacity(0.1),
                DashboardColors.primaryTeal.withOpacity(0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: DashboardColors.primaryOrange.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: DashboardColors.primaryOrange.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.text_fields,
                      color: DashboardColors.primaryOrange,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'ব্যানার টেক্সট এডিটর',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: DashboardColors.textPrimary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _buildBannerTextField(
                controller: _bannerTextController,
                label: 'ব্যানার টেক্সট',
                hint: 'হোমস্ক্রিনের নিচে স্ক্রল হওয়া টেক্সট লিখুন...',
                icon: Icons.text_fields,
                maxLines: 3,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildBannerTextField(
                      controller: _bannerColorWordsController,
                      label: 'রঙিন শব্দ (কমা দিয়ে আলাদা)',
                      hint: 'যেমন: UkilGiri,উকিলগিরি,সালাউদ্দিন',
                      icon: Icons.color_lens,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildBannerTextField(
                      controller: _bannerHyperlinkTextController,
                      label: 'হাইপারলিংক টেক্সট',
                      hint: 'যেমন: আরও জানুন',
                      icon: Icons.link,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildBannerTextField(
                      controller: _bannerHyperlinkUrlController,
                      label: 'হাইপারলিংক URL',
                      hint: 'https://example.com',
                      icon: Icons.web,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Speed control
              _buildBannerTextField(
                controller: _bannerScrollSpeedController,
                label: 'স্ক্রল স্পিড (সেকেন্ড)',
                hint: '20',
                icon: Icons.speed,
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _saveBannerSettings,
                      icon: const Icon(Icons.save, size: 18),
                      label: const Text('সেভ করুন'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: DashboardColors.primaryOrange,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: _previewBannerText,
                    icon: const Icon(Icons.preview, size: 18),
                    label: const Text('প্রিভিউ'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: DashboardColors.primaryTeal,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBannerTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
    TextInputType? keyboardType,
  }) {
    return TextField(
      controller: controller,
      maxLines: maxLines,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, size: 20, color: DashboardColors.primaryOrange),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: DashboardColors.primaryOrange.withOpacity(0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: DashboardColors.primaryOrange),
        ),
        filled: true,
        fillColor: DashboardColors.cardBg.withOpacity(0.5),
        labelStyle: const TextStyle(color: DashboardColors.textSecondary, fontSize: 12),
        hintStyle: const TextStyle(color: DashboardColors.textMuted, fontSize: 12),
      ),
      style: const TextStyle(color: DashboardColors.textPrimary, fontSize: 14),
    );
  }

  Future<void> _saveBannerSettings() async {
    try {
      // Parse scroll speed, default to 20 if invalid
      int scrollSpeed = 20;
      try {
        scrollSpeed = int.parse(_bannerScrollSpeedController.text.trim());
        if (scrollSpeed < 5) scrollSpeed = 5; // Minimum 5 seconds
        if (scrollSpeed > 60) scrollSpeed = 60; // Maximum 60 seconds
      } catch (e) {
        scrollSpeed = 20; // Default value
      }

      await _firestore.collection('admin_settings').doc('banner_text').set({
        'text': _bannerTextController.text.trim(),
        'colorWords': _bannerColorWordsController.text.trim(),
        'hyperlinkText': _bannerHyperlinkTextController.text.trim(),
        'hyperlinkUrl': _bannerHyperlinkUrlController.text.trim(),
        'scrollSpeed': scrollSpeed,
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': _authService.currentUser?.email ?? 'admin',
      }, SetOptions(merge: true));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('ব্যানার টেক্সট সফলভাবে সেভ হয়েছে!'),
            backgroundColor: DashboardColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('ত্রুটি: $e'),
            backgroundColor: DashboardColors.error,
          ),
        );
      }
    }
  }

  void _previewBannerText() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: DashboardColors.cardBg,
        title: const Text(
          'ব্যানার টেক্সট প্রিভিউ',
          style: TextStyle(color: DashboardColors.textPrimary),
        ),
        content: Container(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'টেক্সট:',
                style: TextStyle(
                  color: DashboardColors.textSecondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: DashboardColors.surfaceBg,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: DashboardColors.primaryOrange.withOpacity(0.3)),
                ),
                child: Text(
                  _bannerTextController.text.trim().isEmpty
                    ? 'কোন টেক্সট নেই'
                    : _bannerTextController.text.trim(),
                  style: const TextStyle(
                    color: DashboardColors.textPrimary,
                    fontSize: 12,
                  ),
                ),
              ),
              if (_bannerColorWordsController.text.trim().isNotEmpty) ...[
                const SizedBox(height: 12),
                const Text(
                  'রঙিন শব্দসমূহ:',
                  style: TextStyle(
                    color: DashboardColors.textSecondary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: _bannerColorWordsController.text.split(',').map((word) =>
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: DashboardColors.primaryOrange.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        word.trim(),
                        style: const TextStyle(
                          color: DashboardColors.primaryOrange,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ).toList(),
                ),
              ],
              if (_bannerHyperlinkTextController.text.trim().isNotEmpty) ...[
                const SizedBox(height: 12),
                const Text(
                  'হাইপারলিংক:',
                  style: TextStyle(
                    color: DashboardColors.textSecondary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: DashboardColors.primaryTeal.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '${_bannerHyperlinkTextController.text.trim()} → ${_bannerHyperlinkUrlController.text.trim()}',
                    style: const TextStyle(
                      color: DashboardColors.primaryTeal,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('বন্ধ করুন'),
          ),
        ],
      ),
    );
  }

  // Feedback Management Section
  Widget _buildFeedbackManagementSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            DashboardColors.primaryGreen.withOpacity(0.1),
            DashboardColors.primaryBlue.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: DashboardColors.primaryGreen.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: DashboardColors.primaryGreen.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.feedback_rounded,
                  color: DashboardColors.primaryGreen,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                '💬 ব্যবহারকারীর মতামত',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: DashboardColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Real-time feedback stream
          StreamBuilder<QuerySnapshot>(
            stream: _firestore
                .collection('feedback')
                .orderBy('timestamp', descending: true)
                .limit(10)
                .snapshots(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                return Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(
                    child: Text(
                      'এখনো কোনো মতামত পাওয়া যায়নি',
                      style: TextStyle(
                        color: DashboardColors.textSecondary,
                        fontSize: 14,
                      ),
                    ),
                  ),
                );
              }

              return Column(
                children: [
                  // Feedback statistics
                  _buildFeedbackStats(snapshot.data!.docs),
                  const SizedBox(height: 16),

                  // Feedback list
                  Container(
                    constraints: const BoxConstraints(maxHeight: 400),
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: snapshot.data!.docs.length,
                      itemBuilder: (context, index) {
                        final doc = snapshot.data!.docs[index];
                        final data = doc.data() as Map<String, dynamic>;
                        return _buildFeedbackCard(doc.id, data);
                      },
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  // Build feedback statistics
  Widget _buildFeedbackStats(List<QueryDocumentSnapshot> feedbacks) {
    int totalFeedbacks = feedbacks.length;
    double averageRating = 0;
    Map<int, int> ratingCounts = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0};

    if (totalFeedbacks > 0) {
      int totalRating = 0;
      for (var feedback in feedbacks) {
        final data = feedback.data() as Map<String, dynamic>;
        int rating = data['rating'] ?? 0;
        totalRating += rating;
        ratingCounts[rating] = (ratingCounts[rating] ?? 0) + 1;
      }
      averageRating = totalRating / totalFeedbacks;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.8),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: DashboardColors.primaryGreen.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              children: [
                Text(
                  '$totalFeedbacks',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: DashboardColors.primaryGreen,
                  ),
                ),
                const Text(
                  'মোট মতামত',
                  style: TextStyle(
                    fontSize: 12,
                    color: DashboardColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      averageRating.toStringAsFixed(1),
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(width: 4),
                    const Icon(
                      Icons.star,
                      color: Colors.orange,
                      size: 20,
                    ),
                  ],
                ),
                const Text(
                  'গড় রেটিং',
                  style: TextStyle(
                    fontSize: 12,
                    color: DashboardColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build individual feedback card
  Widget _buildFeedbackCard(String docId, Map<String, dynamic> data) {
    final name = data['name'] ?? 'অজানা';
    final email = data['email'] ?? '';
    final message = data['message'] ?? '';
    final rating = data['rating'] ?? 0;
    final status = data['status'] ?? 'new';

    // Handle timestamp
    DateTime? timestamp;
    if (data['timestamp'] != null) {
      if (data['timestamp'].runtimeType.toString().contains('Timestamp')) {
        timestamp = data['timestamp'].toDate();
      } else if (data['timestamp'] is String) {
        try {
          timestamp = DateTime.parse(data['timestamp']);
        } catch (e) {
          timestamp = DateTime.now();
        }
      }
    }
    timestamp ??= DateTime.now();

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: status == 'new'
              ? DashboardColors.primaryGreen.withOpacity(0.3)
              : Colors.grey.withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with name, rating, and status
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: DashboardColors.textPrimary,
                      ),
                    ),
                    if (email.isNotEmpty)
                      Text(
                        email,
                        style: const TextStyle(
                          fontSize: 12,
                          color: DashboardColors.textSecondary,
                        ),
                      ),
                  ],
                ),
              ),

              // Rating stars
              Row(
                children: List.generate(5, (index) {
                  return Icon(
                    Icons.star,
                    size: 16,
                    color: index < rating
                        ? Colors.orange
                        : Colors.grey.withOpacity(0.3),
                  );
                }),
              ),

              const SizedBox(width: 8),

              // Status badge
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: status == 'new'
                      ? DashboardColors.primaryGreen.withOpacity(0.2)
                      : Colors.grey.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  status == 'new' ? 'নতুন' : 'দেখা হয়েছে',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    color: status == 'new'
                        ? DashboardColors.primaryGreen
                        : Colors.grey[600],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Message
          Text(
            message,
            style: const TextStyle(
              fontSize: 14,
              color: DashboardColors.textPrimary,
              height: 1.4,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 12),

          // Footer with timestamp and actions
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 14,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 4),
              Text(
                _formatTimestamp(timestamp),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),

              const Spacer(),

              // Mark as reviewed button
              if (status == 'new')
                TextButton(
                  onPressed: () => _markFeedbackAsReviewed(docId),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    minimumSize: Size.zero,
                  ),
                  child: const Text(
                    'দেখা হয়েছে',
                    style: TextStyle(
                      fontSize: 12,
                      color: DashboardColors.primaryGreen,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  // Format timestamp for display
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays} দিন আগে';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ঘন্টা আগে';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} মিনিট আগে';
    } else {
      return 'এখনই';
    }
  }

  // Mark feedback as reviewed
  Future<void> _markFeedbackAsReviewed(String docId) async {
    try {
      await _firestore.collection('feedback').doc(docId).update({
        'status': 'reviewed',
        'reviewedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error marking feedback as reviewed: $e');
    }
  }

  // Developer Management Section
  Widget _buildDeveloperManagementSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            DashboardColors.primaryBlue.withOpacity(0.1),
            DashboardColors.primaryPurple.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: DashboardColors.primaryBlue.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: DashboardColors.primaryBlue.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.code_rounded,
                  color: DashboardColors.primaryBlue,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                '👨‍💻 ডেভেলপার তথ্য সম্পাদনা',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: DashboardColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Real-time developer data stream
          StreamBuilder<DocumentSnapshot>(
            stream: _firestore
                .collection('admin_settings')
                .doc('developers')
                .snapshots(),
            builder: (context, snapshot) {
              Map<String, dynamic> developersData = {};

              if (snapshot.hasData && snapshot.data!.exists) {
                developersData = snapshot.data!.data() as Map<String, dynamic>;
              }

              return Column(
                children: [
                  // Developer 1 Section
                  _buildDeveloperEditCard(
                    'developer1',
                    'Salauddin Majumder',
                    developersData['developer1'] ?? {},
                  ),
                  const SizedBox(height: 16),

                  // Developer 2 Section
                  _buildDeveloperEditCard(
                    'developer2',
                    'Meheron Nesa Surovi',
                    developersData['developer2'] ?? {},
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  // Build individual developer edit card
  Widget _buildDeveloperEditCard(String developerId, String defaultName, Map<String, dynamic> data) {
    final nameController = TextEditingController(text: data['name'] ?? defaultName);
    final descriptionController = TextEditingController(text: data['description'] ?? '');
    final websiteController = TextEditingController(text: data['website'] ?? '');

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: DashboardColors.primaryBlue.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            developerId == 'developer1' ? '👨‍💻 ডেভেলপার ১' : '👩‍💻 ডেভেলপার ২',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: DashboardColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),

          // Name Field
          _buildDeveloperTextField(
            controller: nameController,
            label: 'নাম',
            hint: 'ডেভেলপারের নাম লিখুন',
          ),
          const SizedBox(height: 12),

          // Description Field
          _buildDeveloperTextField(
            controller: descriptionController,
            label: 'বিবরণ',
            hint: 'ডেভেলপার সম্পর্কে সংক্ষিপ্ত বিবরণ লিখুন',
            maxLines: 3,
          ),
          const SizedBox(height: 12),

          // Website Field
          _buildDeveloperTextField(
            controller: websiteController,
            label: 'ওয়েবসাইট লিংক',
            hint: 'https://example.com',
          ),
          const SizedBox(height: 16),

          // Save Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _saveDeveloperData(
                developerId,
                nameController.text,
                descriptionController.text,
                websiteController.text,
              ),
              icon: const Icon(Icons.save_rounded, size: 18),
              label: const Text(
                'সংরক্ষণ করুন',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: DashboardColors.primaryBlue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build developer text field
  Widget _buildDeveloperTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: DashboardColors.textPrimary,
          ),
        ),
        const SizedBox(height: 6),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: DashboardColors.primaryBlue, width: 2),
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          ),
        ),
      ],
    );
  }

  // Save developer data to Firebase
  Future<void> _saveDeveloperData(String developerId, String name, String description, String website) async {
    if (name.trim().isEmpty || description.trim().isEmpty || website.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('অনুগ্রহ করে সব ক্ষেত্র পূরণ করুন'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      await _firestore.collection('admin_settings').doc('developers').update({
        developerId: {
          'name': name.trim(),
          'description': description.trim(),
          'website': website.trim(),
        },
        'lastUpdated': FieldValue.serverTimestamp(),
        'updatedBy': 'admin',
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('ডেভেলপার তথ্য সফলভাবে আপডেট হয়েছে'),
            backgroundColor: DashboardColors.primaryGreen,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error saving developer data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('ডেটা সংরক্ষণে সমস্যা হয়েছে। আবার চেষ্টা করুন।'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}