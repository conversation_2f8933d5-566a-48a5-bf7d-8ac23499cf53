// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBhcxvM4nx6Cr78TGf3yd9xDTwdtXWE',
    appId: '1:481856419920:web:abcdef123456',
    messagingSenderId: '481856419920',
    projectId: 'ukilgiri-app',
    authDomain: 'ukilgiri-app.firebaseapp.com',
    storageBucket: 'ukilgiri-app.appspot.com',
    measurementId: 'G-XXXXXXXXXX',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAKYH_eetu1LuLmp3FIrE9uFymdM7vp-lk',
    appId: '1:481856419920:android:694003a936946c59273955',
    messagingSenderId: '481856419920',
    projectId: 'ukilgiri-app',
    storageBucket: 'ukilgiri-app.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBhcxvM4nx6Cr78TGf3yd9xDTwdtXWE',
    appId: '1:481856419920:ios:abcdef123456',
    messagingSenderId: '481856419920',
    projectId: 'ukilgiri-app',
    storageBucket: 'ukilgiri-app.appspot.com',
    iosBundleId: 'com.example.ukilgiri_app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBhcxvM4nx6Cr78TGf3yd9xDTwdtXWE',
    appId: '1:481856419920:macos:abcdef123456',
    messagingSenderId: '481856419920',
    projectId: 'ukilgiri-app',
    storageBucket: 'ukilgiri-app.appspot.com',
    iosBundleId: 'com.example.ukilgiri_app',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBhcxvM4nx6Cr78TGf3yd9xDTwdtXWE',
    appId: '1:481856419920:windows:abcdef123456',
    messagingSenderId: '481856419920',
    projectId: 'ukilgiri-app',
    storageBucket: 'ukilgiri-app.appspot.com',
  );
}
