plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    // Temporarily disable Google Services for APK build
    // id "com.google.gms.google-services"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.example.ukilgiri_app"
    compileSdk flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // UkilGiri - Bengali Legal AI Assistant App
        applicationId "com.example.ukilgiri_app"
        // Optimized for modern Android devices with all features
        minSdkVersion 21
        targetSdkVersion flutter.targetSdkVersion
        versionCode 1
        versionName "1.0.0"

        // Enable multidex for large app with many dependencies
        multiDexEnabled true

        // App metadata
        resValue "string", "app_name", "UkilGiri - উকিলগিরি"
        resValue "string", "app_description", "Bengali Legal AI Assistant"
    }

    buildTypes {
        release {
            // Production release configuration
            signingConfig signingConfigs.debug

            // Optimize APK size and performance
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            // Build optimizations
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
            zipAlignEnabled true
        }

        debug {
            // Debug configuration for development
            debuggable true
            minifyEnabled false
            shrinkResources false
        }
    }
}

flutter {
    source '../..'
}

dependencies {}
