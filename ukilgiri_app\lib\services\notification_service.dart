import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:async';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  StreamSubscription<DocumentSnapshot>? _notificationSubscription;

  // Check and show daily notification if needed
  Future<void> checkAndShowDailyNotification(BuildContext context) async {
    try {
      // Get notification settings from Firestore
      final notificationDoc = await _firestore
          .collection('admin_settings')
          .doc('notifications')
          .get();

      if (!notificationDoc.exists) return;

      final notificationData = notificationDoc.data() as Map<String, dynamic>;
      final isEnabled = notificationData['enabled'] ?? false;

      if (!isEnabled) return;

      // Check if user has already seen notification today
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now().toIso8601String().split('T')[0]; // YYYY-MM-DD format
      final lastShownDate = prefs.getString('last_notification_date') ?? '';
      final todayShownCount = prefs.getInt('notification_count_$today') ?? 0;
      final maxFrequency = notificationData['frequency'] ?? 1;

      // Show notification if it's a new day or frequency limit not reached
      if (lastShownDate != today || todayShownCount < maxFrequency) {
        // Wait a bit for the UI to settle
        await Future.delayed(const Duration(seconds: 2));
        
        if (context.mounted) {
          _showNotificationDialog(
            context: context,
            title: notificationData['title'] ?? 'নোটিফিকেশন',
            message: notificationData['message'] ?? '',
            hyperlinkText: notificationData['hyperlinkText'] ?? '',
            hyperlinkUrl: notificationData['hyperlinkUrl'] ?? '',
          );

          // Update shown count and date
          await prefs.setString('last_notification_date', today);
          await prefs.setInt('notification_count_$today', todayShownCount + 1);
        }
      }
    } catch (e) {
      debugPrint('Error checking daily notification: $e');
    }
  }

  // Show the notification dialog
  void _showNotificationDialog({
    required BuildContext context,
    required String title,
    required String message,
    required String hyperlinkText,
    required String hyperlinkUrl,
  }) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => _buildNotificationDialog(
        context: context,
        title: title,
        message: message,
        hyperlinkText: hyperlinkText,
        hyperlinkUrl: hyperlinkUrl,
      ),
    );
  }

  // Build the attractive notification dialog
  Widget _buildNotificationDialog({
    required BuildContext context,
    required String title,
    required String message,
    required String hyperlinkText,
    required String hyperlinkUrl,
  }) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        margin: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Animated notification icon
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.notifications_active,
                  color: Colors.white,
                  size: 32,
                ),
              ),
              const SizedBox(height: 16),
              
              // Title
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              
              // Message
              if (message.isNotEmpty)
                Text(
                  message,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              const SizedBox(height: 20),
              
              // Action buttons
              Row(
                children: [
                  if (hyperlinkText.isNotEmpty && hyperlinkUrl.isNotEmpty)
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _launchUrl(hyperlinkUrl);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: const Color(0xFF667eea),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                        child: Text(hyperlinkText),
                      ),
                    ),
                  if (hyperlinkText.isNotEmpty && hyperlinkUrl.isNotEmpty)
                    const SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.white,
                        side: const BorderSide(color: Colors.white, width: 2),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                      ),
                      child: const Text('ঠিক আছে'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Launch URL
  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        debugPrint('Could not launch $url');
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }

  // Setup real-time listener for notification changes
  void setupRealtimeListener(BuildContext context) {
    _notificationSubscription?.cancel();
    
    _notificationSubscription = _firestore
        .collection('admin_settings')
        .doc('notifications')
        .snapshots()
        .listen((snapshot) {
      if (snapshot.exists && context.mounted) {
        final data = snapshot.data() as Map<String, dynamic>;
        final isEnabled = data['enabled'] ?? false;
        
        if (isEnabled) {
          // Check if this is a new notification update
          _checkForNotificationUpdate(context, data);
        }
      }
    });
  }

  // Check for notification updates
  Future<void> _checkForNotificationUpdate(BuildContext context, Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUpdateTime = prefs.getInt('last_notification_update') ?? 0;
      final currentUpdateTime = (data['updatedAt'] as Timestamp?)?.millisecondsSinceEpoch ?? 0;
      
      // If notification was updated recently (within last 5 minutes) and user hasn't seen it
      if (currentUpdateTime > lastUpdateTime && 
          currentUpdateTime > DateTime.now().millisecondsSinceEpoch - (5 * 60 * 1000)) {
        
        await Future.delayed(const Duration(seconds: 1));
        
        if (context.mounted) {
          _showNotificationDialog(
            context: context,
            title: data['title'] ?? 'নোটিফিকেশন',
            message: data['message'] ?? '',
            hyperlinkText: data['hyperlinkText'] ?? '',
            hyperlinkUrl: data['hyperlinkUrl'] ?? '',
          );
          
          await prefs.setInt('last_notification_update', currentUpdateTime);
        }
      }
    } catch (e) {
      debugPrint('Error checking notification update: $e');
    }
  }

  // Dispose resources
  void dispose() {
    _notificationSubscription?.cancel();
  }
}
