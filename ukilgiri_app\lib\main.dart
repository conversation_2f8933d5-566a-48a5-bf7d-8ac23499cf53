//main

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/gestures.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:convert';
import 'dart:math' as math;
import 'dart:ui';
import 'dart:async';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'services/auth_service.dart';
import 'services/notification_service.dart';
import 'screens/auth_screen.dart';
import 'screens/email_verification_screen.dart';
import 'screens/profile_screen.dart';
import 'screens/premium_screen.dart';
import 'screens/admin_dashboard_screen.dart';
import 'firebase_options.dart';
import 'theme/app_theme.dart';
import 'widgets/enhanced_components.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  runApp(const UkilGiriApp());
}

class UkilGiriApp extends StatelessWidget {
  const UkilGiriApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'UkilGiri - উকিলগিরি',
      theme: ThemeData(
        // Using Hind Siliguri Bengali font (same as index.html)
        useMaterial3: true,
        fontFamily: 'HindSiliguri',
        textTheme: const TextTheme(
          // Define custom text styles with better weights for Bengali text
          displayLarge: TextStyle(
            fontFamily: 'HindSiliguri',
            fontWeight: FontWeight.bold,
            fontSize: 32,
            height: 1.3,
          ),
          headlineLarge: TextStyle(
            fontFamily: 'HindSiliguri',
            fontWeight: FontWeight.w600,
            fontSize: 24,
            height: 1.4,
          ),
          bodyLarge: TextStyle(
            fontFamily: 'HindSiliguri',
            fontWeight: FontWeight.w400,
            fontSize: 16,
            height: 1.6,
          ),
          bodyMedium: TextStyle(
            fontFamily: 'HindSiliguri',
            fontWeight: FontWeight.w400,
            fontSize: 14,
            height: 1.5,
          ),
        ),
        // Custom scrollbar theme for better visibility and transparency
        scrollbarTheme: ScrollbarThemeData(
          thumbVisibility: MaterialStateProperty.all(true),
          trackVisibility: MaterialStateProperty.all(true),
          thickness: MaterialStateProperty.all(8.0),
          radius: const Radius.circular(4),
          thumbColor: MaterialStateProperty.all(
            AppColors.primaryGreen.withValues(alpha: 0.6),
          ),
          trackColor: MaterialStateProperty.all(
            AppColors.primaryGreen.withValues(alpha: 0.1),
          ),
          trackBorderColor: MaterialStateProperty.all(
            AppColors.primaryGreen.withValues(alpha: 0.2),
          ),
        ),

        // UNCOMMENT THESE LINES AFTER DOWNLOADING BENGALI FONTS:
        // fontFamily: 'SolaimanLipi',
        // fontFamilyFallback: const ['Kalpurush', 'Roboto', 'Arial'],
      ),
      home: const AppWrapper(),
      debugShowCheckedModeBanner: false,
    );
  }
}

// Main app wrapper to handle landing page and auth flow
class AppWrapper extends StatefulWidget {
  const AppWrapper({super.key});

  @override
  State<AppWrapper> createState() => _AppWrapperState();
}

class _AppWrapperState extends State<AppWrapper> {
  bool _showLandingPage = true;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkFirstTimeUser();
  }

  // Check if this is the first time user opens the app
  Future<void> _checkFirstTimeUser() async {
    final prefs = await SharedPreferences.getInstance();
    final hasSeenLanding = prefs.getBool('has_seen_landing') ?? false;

    setState(() {
      _showLandingPage = !hasSeenLanding;
      _isLoading = false;
    });
  }

  // Mark landing page as seen
  Future<void> _markLandingPageSeen() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('has_seen_landing', true);

    setState(() {
      _showLandingPage = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        backgroundColor: AppColors.darkBg,
        body: Center(
          child: EnhancedLoadingAnimation(
            message: 'লোড হচ্ছে...',
          ),
        ),
      );
    }

    if (_showLandingPage) {
      return LandingPage(onGetStarted: _markLandingPageSeen);
    }

    return const AuthWrapper();
  }
}

// Authentication wrapper to handle auth state
class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        // Show loading while checking auth state
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            backgroundColor: AppColors.darkBg,
            body: Center(
              child: EnhancedLoadingAnimation(
                message: 'লোড হচ্ছে...',
              ),
            ),
          );
        }

        // Show main app if user is signed in and email is verified
        if (snapshot.hasData) {
          final user = snapshot.data!;
          debugPrint('AuthWrapper: User found - ${user.email}, Email verified: ${user.emailVerified}');
          if (user.emailVerified) {
            // Check if user is admin
            final authService = AuthService();
            final isAdmin = authService.isAdmin();
            debugPrint('AuthWrapper: Is admin: $isAdmin');
            if (isAdmin) {
              debugPrint('AuthWrapper: Navigating to AdminDashboardScreen');
              return const AdminDashboardScreen();
            } else {
              debugPrint('AuthWrapper: Navigating to UkilGiriHomePage');
              return const UkilGiriHomePage();
            }
          } else {
            debugPrint('AuthWrapper: Email not verified, showing EmailVerificationScreen');
            // Show email verification screen if email is not verified
            return const EmailVerificationScreen();
          }
        }

        // Show auth screen if user is not signed in
        return const AuthScreen();
      },
    );
  }
}

// Landing Page Widget
class LandingPage extends StatefulWidget {
  final VoidCallback onGetStarted;

  const LandingPage({super.key, required this.onGetStarted});

  @override
  State<LandingPage> createState() => _LandingPageState();
}

class _LandingPageState extends State<LandingPage> with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    )..repeat();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // Scrollable content
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        const SizedBox(height: 20),

                        // 3D Logo with animation
                        _build3DLogo(),

                        const SizedBox(height: 30),

                        // Welcome text
                        _buildWelcomeText(),

                        const SizedBox(height: 25),

                        // Feature cards
                        _buildFeatureCards(),

                        const SizedBox(height: 30),
                      ],
                    ),
                  ),
                ),

                // Fixed bottom section with buttons
                Column(
                  children: [
                    // Get started button
                    _buildGetStartedButton(),

                    const SizedBox(height: 10),

                    // Skip button
                    _buildSkipButton(),

                    const SizedBox(height: 10),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Build 3D logo with animation
  Widget _build3DLogo() {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Transform(
          alignment: Alignment.center,
          transform: Matrix4.identity()
            ..setEntry(3, 2, 0.001)
            ..rotateY(_rotationController.value * 0.3)
            ..scale(1.0 + _pulseController.value * 0.1),
          child: Container(
            padding: const EdgeInsets.all(30),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primaryGreen.withValues(alpha: 0.2),
                  AppColors.primaryPurple.withValues(alpha: 0.2),
                ],
              ),
              border: Border.all(
                color: AppColors.primaryGreen.withValues(alpha: 0.3),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryGreen.withValues(alpha: 0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildGradientText('Ukil', [AppColors.primaryGreen, AppColors.primaryPurple]),
                    _buildGradientText('Giri', [const Color(0xFFff3e9d), const Color(0xFF0e8aff)]),
                  ],
                ),
                const SizedBox(height: 10),
                ShaderMask(
                  shaderCallback: (bounds) => const LinearGradient(
                    colors: [AppColors.primaryGreen, AppColors.primaryPurple],
                  ).createShader(bounds),
                  child: const Text(
                    'উকিলগিরি',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Build gradient text widget
  Widget _buildGradientText(String text, List<Color> colors) {
    return ShaderMask(
      shaderCallback: (bounds) => LinearGradient(
        colors: colors,
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ).createShader(bounds),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w800,
          color: Colors.white,
        ),
      ),
    );
  }

  // Build welcome text with animation
  Widget _buildWelcomeText() {
    return Column(
      children: [
        ShaderMask(
          shaderCallback: (bounds) => const LinearGradient(
            colors: [AppColors.primaryGreen, AppColors.primaryPurple, Color(0xFFff3e9d)],
          ).createShader(bounds),
          child: const Text(
            'বাংলাদেশের প্রথম AI আইনি সহায়ক',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 20),
        const Text(
          'আইনি জ্ঞানকে সবার কাছে পৌঁছে দেওয়ার আধুনিক সমাধান',
          style: TextStyle(
            fontSize: 18,
            color: AppColors.textPrimary,
            height: 1.6,
            fontWeight: FontWeight.w400,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // Build feature cards
  Widget _buildFeatureCards() {
    return Column(
      children: [
        _buildFeatureCard(
          Icons.chat_bubble_outline,
          'AI চ্যাট সহায়তা',
          'বাংলাদেশের আইন সম্পর্কে তাৎক্ষণিক উত্তর পান',
          [AppColors.primaryGreen, AppColors.primaryPurple],
        ),
        const SizedBox(height: 15),
        _buildFeatureCard(
          Icons.search,
          'উকিল খুঁজুন',
          'আপনার এলাকার যোগ্য আইনজীবীদের খুঁজে নিন',
          [AppColors.primaryPurple, const Color(0xFFff3e9d)],
        ),
        const SizedBox(height: 15),
        _buildFeatureCard(
          Icons.library_books,
          'আইনি তথ্য',
          'বাংলাদেশের সংবিধান ও আইনের বিস্তারিত তথ্য',
          [const Color(0xFFff3e9d), const Color(0xFF0e8aff)],
        ),
      ],
    );
  }

  // Build individual feature card
  Widget _buildFeatureCard(IconData icon, String title, String description, List<Color> colors) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        gradient: LinearGradient(
          colors: colors.map((c) => c.withOpacity(0.1)).toList(),
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: Border.all(
          color: colors[0].withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(colors: colors),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 5),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textPrimary.withOpacity(0.8),
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build get started button
  Widget _buildGetStartedButton() {
    return AnimatedButton(
      text: 'শুরু করুন',
      onPressed: widget.onGetStarted,
      width: double.infinity,
      height: 50,
      gradient: const LinearGradient(
        colors: [AppColors.primaryGreen, AppColors.primaryPurple],
      ),
      icon: Icons.arrow_forward,
    );
  }

  // Build skip button
  Widget _buildSkipButton() {
    return TextButton(
      onPressed: widget.onGetStarted,
      child: const Text(
        'এড়িয়ে যান',
        style: TextStyle(
          color: AppColors.textSecondary,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}



// Message model
class ChatMessage {
  final String content;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({
    required this.content,
    required this.isUser,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'content': content,
      'isUser': isUser,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      content: json['content'],
      isUser: json['isUser'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

// Conversation model
class Conversation {
  final String id;
  String title;
  final List<ChatMessage> messages;
  final DateTime? timestamp;

  Conversation({
    required this.id,
    required this.title,
    required this.messages,
    this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'messages': messages.map((m) => m.toJson()).toList(),
      if (timestamp != null) 'timestamp': timestamp!.toIso8601String(),
    };
  }

  factory Conversation.fromJson(Map<String, dynamic> json) {
    DateTime? parsedTimestamp;

    // Handle different timestamp formats from Firebase
    if (json['timestamp'] != null) {
      if (json['timestamp'] is String) {
        try {
          parsedTimestamp = DateTime.parse(json['timestamp']);
        } catch (e) {
          // If parsing fails, use current time
          parsedTimestamp = DateTime.now();
        }
      } else if (json['timestamp'].runtimeType.toString().contains('Timestamp')) {
        // Firebase Timestamp object
        parsedTimestamp = json['timestamp'].toDate();
      }
    }

    return Conversation(
      id: json['id'],
      title: json['title'],
      messages: (json['messages'] as List)
          .map((m) => ChatMessage.fromJson(m))
          .toList(),
      timestamp: parsedTimestamp,
    );
  }
}

// Main home page
class UkilGiriHomePage extends StatefulWidget {
  const UkilGiriHomePage({super.key});

  @override
  State<UkilGiriHomePage> createState() => _UkilGiriHomePageState();
}

// Enum for different pages
enum AppPage { chat, instructions, about, contact, findUkil, premium, profile }

class _UkilGiriHomePageState extends State<UkilGiriHomePage> with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _chatScrollController = ScrollController();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final AuthService _authService = AuthService();
  final NotificationService _notificationService = NotificationService();

  List<Conversation> _conversations = [];
  String _currentConversationId = '';
  List<ChatMessage> _currentMessages = [];
  bool _isLoading = false;
  bool _isAIResponding = false; // Track if AI is currently responding
  bool _isTypingAnimation = false; // Track if AI is typing animation
  String _currentTypingText = ''; // Current text being typed
  Timer? _typingTimer; // Timer for typing animation
  AppPage _currentPage = AppPage.chat;
  bool _sortAscending = false; // Sort order for conversation history (false = newest first)

  // Find Ukil page state
  final TextEditingController _locationController = TextEditingController();
  bool _isSearchingUkils = false;
  List<Map<String, dynamic>> _foundUkils = [];

  // Feedback form controllers
  final TextEditingController _feedbackNameController = TextEditingController();
  final TextEditingController _feedbackEmailController = TextEditingController();
  final TextEditingController _feedbackMessageController = TextEditingController();
  int _feedbackRating = 5;
  bool _isSubmittingFeedback = false;





  // Animation controllers for 3D loading animation
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late AnimationController _scrollController;
  late AnimationController _glowController;

  // API configuration
  static const String apiKey = "AIzaSyDXxWIwHQoYbs3Fl7Q4iq2r7hSVLhyMlBs";
  static const String apiUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";

  @override
  void initState() {
    super.initState();
    _loadConversations();
    _startNewConversation();

    // Initialize notification service
    _initializeNotificationService();

    // Initialize animation controllers
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);

    _scrollController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _glowController = AnimationController(
      duration: const Duration(milliseconds: 4000), // Slower 4-second loop
      vsync: this,
    )..repeat();

    // Add listener to message controller for send button reactivity
    _messageController.addListener(() {
      setState(() {
        // This will trigger rebuild and make send button react to text changes
      });
    });
  }



  @override
  void dispose() {
    _typingTimer?.cancel(); // Cancel typing timer
    _notificationService.dispose(); // Dispose notification service
    _messageController.dispose();
    _locationController.dispose();
    _chatScrollController.dispose();
    _rotationController.dispose();
    _pulseController.dispose();
    _scrollController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  // Initialize notification service
  void _initializeNotificationService() {
    // Setup real-time listener for notification changes
    _notificationService.setupRealtimeListener(context);

    // Check and show daily notification after a short delay
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _notificationService.checkAndShowDailyNotification(context);
      }
    });
  }

  // Load conversations from Firebase for current user
  Future<void> _loadConversations() async {
    try {
      if (_authService.currentUser != null) {
        final chatHistory = await _authService.getUserChatHistory();
        setState(() {
          _conversations = chatHistory.map((c) => Conversation.fromJson(c)).toList();
          _sortConversations();
        });
      } else {
        // If no user is logged in, load from SharedPreferences as fallback
        final prefs = await SharedPreferences.getInstance();
        final conversationsJson = prefs.getString('ukilgiri_conversations');
        if (conversationsJson != null) {
          final List<dynamic> decoded = json.decode(conversationsJson);
          setState(() {
            _conversations = decoded.map((c) => Conversation.fromJson(c)).toList();
            _sortConversations();
          });
        }
      }
    } catch (e) {
      debugPrint('Error loading conversations: $e');
      // Fallback to SharedPreferences if Firebase fails
      final prefs = await SharedPreferences.getInstance();
      final conversationsJson = prefs.getString('ukilgiri_conversations');
      if (conversationsJson != null) {
        final List<dynamic> decoded = json.decode(conversationsJson);
        setState(() {
          _conversations = decoded.map((c) => Conversation.fromJson(c)).toList();
          _sortConversations();
        });
      }
    }
  }

  // Save conversations to Firebase for current user
  Future<void> _saveConversations() async {
    try {
      if (_authService.currentUser != null) {
        // Save to Firebase - we don't need to save all conversations at once
        // Individual conversations are saved in _saveCurrentConversation
        return;
      } else {
        // If no user is logged in, save to SharedPreferences as fallback
        final prefs = await SharedPreferences.getInstance();
        final conversationsJson = json.encode(_conversations.map((c) => c.toJson()).toList());
        await prefs.setString('ukilgiri_conversations', conversationsJson);
      }
    } catch (e) {
      debugPrint('Error saving conversations: $e');
      // Fallback to SharedPreferences if Firebase fails
      final prefs = await SharedPreferences.getInstance();
      final conversationsJson = json.encode(_conversations.map((c) => c.toJson()).toList());
      await prefs.setString('ukilgiri_conversations', conversationsJson);
    }
  }

  // Sort conversations based on current sort order
  void _sortConversations() {
    _conversations.sort((a, b) {
      final aTime = a.timestamp ?? DateTime.now();
      final bTime = b.timestamp ?? DateTime.now();
      return _sortAscending ? aTime.compareTo(bTime) : bTime.compareTo(aTime);
    });
  }

  // Toggle sort order and refresh conversation list
  void _toggleSortOrder() {
    setState(() {
      _sortAscending = !_sortAscending;
      _sortConversations();
    });
  }

  // Navigate to different pages
  void _navigateToPage(AppPage page) {
    setState(() {
      _currentPage = page;
    });

    // Close drawer on mobile
    if (_scaffoldKey.currentState?.isDrawerOpen == true) {
      Navigator.of(context).pop();
    }
  }

  // Start a new conversation
  void _startNewConversation() {
    setState(() {
      _currentConversationId = DateTime.now().millisecondsSinceEpoch.toString();
      _currentMessages = [];
      _currentPage = AppPage.chat; // Ensure we're on chat page
    });
  }

  // Load an existing conversation
  void _loadConversation(String conversationId) {
    final conversation = _conversations.firstWhere(
      (c) => c.id == conversationId,
      orElse: () => Conversation(id: conversationId, title: 'নতুন আলোচনা', messages: [], timestamp: DateTime.now()),
    );

    setState(() {
      _currentConversationId = conversationId;
      _currentMessages = List.from(conversation.messages);
    });

    // Close drawer on mobile
    if (_scaffoldKey.currentState?.isDrawerOpen == true) {
      Navigator.of(context).pop();
    }
  }

  // Delete a conversation
  Future<void> _deleteConversation(String conversationId) async {
    try {
      if (_authService.currentUser != null) {
        await _authService.deleteChatConversation(conversationId);
      }

      setState(() {
        _conversations.removeWhere((c) => c.id == conversationId);
        if (_currentConversationId == conversationId) {
          _startNewConversation();
        }
      });

      await _saveConversations();
    } catch (e) {
      debugPrint('Error deleting conversation: $e');
      // Still remove from local state even if Firebase fails
      setState(() {
        _conversations.removeWhere((c) => c.id == conversationId);
        if (_currentConversationId == conversationId) {
          _startNewConversation();
        }
      });
      await _saveConversations();
    }
  }

  // Send message to AI
  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty || _isLoading) return;

    // Check daily chat limit before proceeding
    final todaysChatCount = await _authService.getTodaysChatCount();
    if (todaysChatCount >= 15) {
      _showDailyLimitDialog();
      return;
    }

    // Add user message
    final userMessage = ChatMessage(
      content: message,
      isUser: true,
      timestamp: DateTime.now(),
    );

    setState(() {
      _currentMessages.add(userMessage);
      _isLoading = true;
      _isAIResponding = true; // Mark that AI is responding
    });

    _messageController.clear();

    // Dismiss keyboard
    FocusScope.of(context).unfocus();

    _scrollToBottom();

    try {
      // Create enhanced legal prompt
      final legalPrompt = '''You are UkilGiri, a legal assistant AI specializing in Bangladesh law.

User question: "$message"

Please provide a short yet detailed, structured and easy-to-understand answer with the following priorities:
1. HIGHEST PRIORITY: ALWAYS RESPOND IN PURE BENGALI LANGUAGE ONLY. NEVER USE ANY ENGLISH OR BANGLISH WORDS. Use Bengali words for all concepts, including legal terms.
2. Reference relevant sections from the Constitution of Bangladesh when applicable - include article numbers when referencing the Constitution
3. Include citations to specific laws, acts, and legal precedents - mention section numbers, act names, and years
4. For each legal point, provide the exact reference
5. Explain legal concepts in simple terms that a non-lawyer can understand
6. Provide practical guidance on what steps the person might take
7. Use bullet points and clear organization for complex information
8. Include both the legal perspective and practical real-world considerations

Your response MUST be rich with specific legal references while remaining accessible.

REMEMBER: ALWAYS RESPOND IN PURE BENGALI LANGUAGE ONLY. NEVER USE ANY ENGLISH OR BANGLISH WORDS. The only exceptions are for proper nouns, law names, and section numbers that cannot be translated.''';

      // Make API call
      final response = await http.post(
        Uri.parse('$apiUrl?key=$apiKey'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'contents': [{
            'parts': [{'text': legalPrompt}]
          }]
        }),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final aiResponse = responseData['candidates'][0]['content']['parts'][0]['text'];

        // Calculate and update character count for AI response
        final charactersUsed = _authService.calculateCharacters(aiResponse);
        await _authService.updateCharacterCount(charactersUsed);

        // Count AI response (this should happen every time user gets an AI response)
        await _authService.countAIResponse();

        // Start real-time typing animation
        _startTypingAnimation(aiResponse, userMessage.content);

        // Save conversation
        _saveCurrentConversation(message);
      } else {
        throw Exception('API request failed with status: ${response.statusCode}');
      }
    } catch (e) {
      // Add error message with more details for debugging
      String errorText = 'দুঃখিত, আপনার প্রশ্নের উত্তর দিতে একটি সমস্যা হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন।';

      // Add debug info in development
      if (e.toString().contains('SocketException') || e.toString().contains('network')) {
        errorText += '\n\nনেটওয়ার্ক সংযোগ পরীক্ষা করুন।';
      } else if (e.toString().contains('status: 4')) {
        errorText += '\n\nAPI সীমা অতিক্রম হয়েছে।';
      }

      final errorMessage = ChatMessage(
        content: errorText,
        isUser: false,
        timestamp: DateTime.now(),
      );

      setState(() {
        _currentMessages.add(errorMessage);
        _isLoading = false;
        _isAIResponding = false; // Reset flag on error too
      });
    }
  }

  // Show daily chat limit dialog
  void _showDailyLimitDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.cardBg,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(
              Icons.chat_bubble_outline,
              color: AppColors.accentOrange,
              size: 28,
            ),
            const SizedBox(width: 12),
            const Text(
              'দৈনিক চ্যাট সীমা',
              style: TextStyle(
                color: AppColors.textPrimary,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'আপনি আজকের জন্য ১০টি চ্যাটের সীমা অতিক্রম করেছেন।',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 16,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'আগামীকাল নতুন ১০টি চ্যাট পাবেন।',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.accentOrange,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('ঠিক আছে'),
          ),
        ],
      ),
    );
  }

  // Save current conversation
  Future<void> _saveCurrentConversation(String firstMessage) async {
    final existingIndex = _conversations.indexWhere((c) => c.id == _currentConversationId);

    String title = firstMessage.length > 30
        ? '${firstMessage.substring(0, 30)}...'
        : firstMessage;

    final conversation = Conversation(
      id: _currentConversationId,
      title: title,
      messages: List.from(_currentMessages),
      timestamp: DateTime.now(),
    );

    try {
      if (_authService.currentUser != null) {
        // Save to Firebase
        if (existingIndex >= 0) {
          // Update existing conversation - remove from old position and add to top
          await _authService.updateChatConversation(_currentConversationId, conversation.toJson());
          _conversations.removeAt(existingIndex);
          _conversations.insert(0, conversation);
        } else {
          // Save new conversation
          await _authService.saveChatConversation(conversation.toJson());
          _conversations.insert(0, conversation);
        }
      } else {
        // Fallback to local storage
        if (existingIndex >= 0) {
          // Update existing conversation - remove from old position and add to top
          _conversations.removeAt(existingIndex);
          _conversations.insert(0, conversation);
        } else {
          _conversations.insert(0, conversation);
        }
        await _saveConversations();
      }
    } catch (e) {
      debugPrint('Error saving conversation: $e');
      // Fallback to local state update
      if (existingIndex >= 0) {
        // Update existing conversation - remove from old position and add to top
        _conversations.removeAt(existingIndex);
        _conversations.insert(0, conversation);
      } else {
        _conversations.insert(0, conversation);
      }
      await _saveConversations();
    }
  }

  // Scroll to bottom of chat
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_chatScrollController.hasClients) {
        _chatScrollController.animateTo(
          _chatScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  // Start real-time typing animation for AI response
  void _startTypingAnimation(String fullResponse, String userQuestion) {
    // Cancel any existing timer
    _typingTimer?.cancel();

    setState(() {
      _isTypingAnimation = true;
      _currentTypingText = '';
      _isLoading = false;
      _isAIResponding = true;
    });

    // Add initial AI message with empty content
    final aiMessage = ChatMessage(
      content: '',
      isUser: false,
      timestamp: DateTime.now(),
    );

    setState(() {
      _currentMessages.add(aiMessage);
    });

    // Scroll to show the AI response header
    _scrollToAIResponseStart();

    // Split response into words for realistic typing
    final words = fullResponse.split(' ');
    int currentWordIndex = 0;

    // Start typing animation with faster speed
    _typingTimer = Timer.periodic(const Duration(milliseconds: 50), (timer) {
      if (currentWordIndex < words.length) {
        setState(() {
          if (currentWordIndex == 0) {
            _currentTypingText = words[currentWordIndex];
          } else {
            _currentTypingText += ' ${words[currentWordIndex]}';
          }

          // Update the last message content
          if (_currentMessages.isNotEmpty && !_currentMessages.last.isUser) {
            _currentMessages.last = ChatMessage(
              content: _currentTypingText,
              isUser: false,
              timestamp: _currentMessages.last.timestamp,
            );
          }
        });

        currentWordIndex++;

        // Auto-scroll to follow the typing
        _autoScrollDuringTyping();

      } else {
        // Typing complete
        timer.cancel();
        setState(() {
          _isTypingAnimation = false;
          _isAIResponding = false;
        });

        // Final scroll to show the first line of AI response
        _scrollToFirstLineOfResponse();
      }
    });
  }

  // Scroll to show AI response header at the beginning
  void _scrollToAIResponseStart() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_chatScrollController.hasClients && _currentMessages.isNotEmpty) {
        // Find the position of the last AI message (newest one)
        int lastAIMessageIndex = -1;
        for (int i = _currentMessages.length - 1; i >= 0; i--) {
          if (!_currentMessages[i].isUser) {
            lastAIMessageIndex = i;
            break;
          }
        }

        if (lastAIMessageIndex != -1) {
          // Position AI response at top 20% of screen for optimal reading
          final maxScrollExtent = _chatScrollController.position.maxScrollExtent;
          final viewportHeight = _chatScrollController.position.viewportDimension;

          // Calculate target position to show AI response header
          final targetPosition = maxScrollExtent - (viewportHeight * 0.8);
          final scrollTarget = targetPosition.clamp(0.0, maxScrollExtent);

          _chatScrollController.animateTo(
            scrollTarget,
            duration: const Duration(milliseconds: 400),
            curve: Curves.easeOut,
          );
        }
      }
    });
  }

  // Auto-scroll during typing to follow the content
  void _autoScrollDuringTyping() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_chatScrollController.hasClients) {
        // Gentle scroll to keep up with typing
        final maxScrollExtent = _chatScrollController.position.maxScrollExtent;
        final currentPosition = _chatScrollController.position.pixels;

        // Only scroll if we're near the bottom or following the AI response
        if (maxScrollExtent - currentPosition < 200) {
          _chatScrollController.animateTo(
            maxScrollExtent,
            duration: const Duration(milliseconds: 100),
            curve: Curves.easeOut,
          );
        }
      }
    });
  }

  // Scroll to bottom after AI response completion
  void _scrollToFirstLineOfResponse() {
    // After AI completes typing, scroll to the bottom (last position)
    Future.delayed(const Duration(milliseconds: 200), () => _scrollToBottom());
    Future.delayed(const Duration(milliseconds: 600), () => _scrollToBottom());
    Future.delayed(const Duration(milliseconds: 1200), () => _scrollToBottom());
  }

  // Simple and direct scroll to user question
  void _scrollToUserQuestion() {
    if (_chatScrollController.hasClients && _currentMessages.isNotEmpty) {
      final maxScrollExtent = _chatScrollController.position.maxScrollExtent;
      final viewportHeight = _chatScrollController.position.viewportDimension;

      print('Attempting scroll: max=$maxScrollExtent, viewport=$viewportHeight');

      // Try different scroll positions to find one that works
      double targetPosition;

      if (maxScrollExtent > viewportHeight) {
        // For longer conversations, scroll to show last 2-3 messages
        targetPosition = maxScrollExtent - (viewportHeight * 0.8);
      } else {
        // For shorter conversations, scroll to top
        targetPosition = 0.0;
      }

      final scrollTarget = targetPosition.clamp(0.0, maxScrollExtent);

      print('Scrolling to position: $scrollTarget');

      _chatScrollController.animateTo(
        scrollTarget,
        duration: const Duration(milliseconds: 1000),
        curve: Curves.easeInOut,
      );
    }
  }

  void _attemptScrollToFirstLine() {
    if (_chatScrollController.hasClients && _currentMessages.isNotEmpty) {
      final maxScrollExtent = _chatScrollController.position.maxScrollExtent;
      final viewportHeight = _chatScrollController.position.viewportDimension;
      final currentPosition = _chatScrollController.position.pixels;

      print('Scroll attempt: max=$maxScrollExtent, viewport=$viewportHeight, current=$currentPosition');

      // Find the user's question (second to last message) and AI response (last message)
      if (_currentMessages.length >= 2) {
        // Calculate position to show both user question and start of AI response
        // Each message bubble is approximately 100-300px depending on content
        // We want to show the user question at the top and AI response beginning

        // Approach: Scroll to show the conversation pair (user question + AI response start)
        // Position the user question at top 10% of screen, AI response follows
        final targetPosition = maxScrollExtent - (viewportHeight * 0.9);
        final scrollTarget = targetPosition.clamp(0.0, maxScrollExtent);

        print('Scrolling to show user question: target=$scrollTarget');

        _chatScrollController.animateTo(
          scrollTarget,
          duration: const Duration(milliseconds: 800),
          curve: Curves.easeInOut,
        );
      } else {
        // Fallback for single message
        final targetPosition = maxScrollExtent - (viewportHeight * 0.7);
        final scrollTarget = targetPosition.clamp(0.0, maxScrollExtent);

        _chatScrollController.animateTo(
          scrollTarget,
          duration: const Duration(milliseconds: 800),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  // Fallback method - simple scroll up from current position
  void _fallbackScrollUp() {
    if (_chatScrollController.hasClients) {
      final currentPosition = _chatScrollController.position.pixels;
      final viewportHeight = _chatScrollController.position.viewportDimension;

      // Simple approach: scroll up by 60% of viewport height
      final scrollUpAmount = viewportHeight * 0.6;
      final targetPosition = (currentPosition - scrollUpAmount).clamp(0.0, _chatScrollController.position.maxScrollExtent);

      print('Fallback scroll: current=$currentPosition, scrollUp=$scrollUpAmount, target=$targetPosition');

      _chatScrollController.animateTo(
        targetPosition,
        duration: const Duration(milliseconds: 1000),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBackground(
      child: _buildMagicalBorderWrapper(
        child: Scaffold(
          key: _scaffoldKey,
          backgroundColor: Colors.transparent,
          appBar: _buildAppBar(),
          drawer: _buildSidebar(),
          body: Column(
            children: [
              _buildAnimatedBanner(),
              Expanded(
                child: _buildCurrentPage(),
              ),
              if (_currentPage == AppPage.chat) _buildInputArea(),
            ],
          ),
        ),
      ),
    );
  }

  // Build magical glowing border wrapper during AI response
  Widget _buildMagicalBorderWrapper({required Widget child}) {
    if (!_isAIResponding) {
      return child;
    }

    return AnimatedBuilder(
      animation: Listenable.merge([_pulseController, _glowController, _rotationController]),
      builder: (context, _) {
        return Stack(
          children: [
            child,
            // Single thin glowing border overlay
            Positioned.fill(
              child: IgnorePointer(
                child: CustomPaint(
                  painter: MagicalBorderPainter(
                    glowProgress: _glowController.value,
                    rotationProgress: _rotationController.value,
                    pulseValue: _pulseController.value,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // Build app bar - minimal design with just app name and small tagline
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.sidebarBg,
      foregroundColor: AppColors.textPrimary,
      toolbarHeight: 60, // Reduce toolbar height
      automaticallyImplyLeading: false, // Disable automatic leading widget
      leading: IconButton(
        onPressed: (_isLoading || _isAIResponding) ? null : () {
          _scaffoldKey.currentState?.openDrawer();
        },
        icon: const Icon(Icons.menu),
        tooltip: 'মেনু',
      ),
      title: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // App name in a single row with smaller fonts
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildCompactGradientText('Ukil', [AppColors.primaryGreen, AppColors.primaryPurple]),
              _buildCompactGradientText('Giri', [const Color(0xFFff3e9d), const Color(0xFF0e8aff)]),
            ],
          ),
          // Small tagline below
          const Text(
            'আইনি সহায়তার নতুন দিগন্ত',
            style: TextStyle(
              fontSize: 10,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
      actions: [
        _buildPremiumButton(),
        _buildChatCounter(),
        _buildProfileButton(),
        IconButton(
          onPressed: (_isLoading || _isAIResponding) ? null : _startNewConversation,
          icon: const Icon(Icons.add, size: 20),
          tooltip: 'নতুন আলোচনা',
        ),
      ],
    );
  }

  // Build chat counter widget
  Widget _buildChatCounter() {
    return StreamBuilder<int>(
      stream: _authService.getTodaysChatCountStream(),
      builder: (context, snapshot) {
        final todayChats = snapshot.data ?? 0;
        final remainingChats = 10 - todayChats;

        return Container(
          margin: const EdgeInsets.only(right: 8),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: remainingChats <= 3
                ? Colors.red.withOpacity(0.1)
                : Colors.green.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: remainingChats <= 3
                  ? Colors.red.withOpacity(0.3)
                  : AppColors.primaryGreen.withOpacity(0.3),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.chat_bubble_outline,
                size: 16,
                color: remainingChats <= 3 ? Colors.red : AppColors.primaryGreen,
              ),
              Text(
                '$remainingChats',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: remainingChats <= 3 ? Colors.red : AppColors.primaryGreen,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Build professional profile button with user avatar and name
  Widget _buildProfileButton() {
    return GestureDetector(
      onTap: (_isLoading || _isAIResponding) ? null : () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const ProfileScreen()),
        );
      },
      child: FutureBuilder<Map<String, dynamic>?>(
        future: _authService.getUserData(),
        builder: (context, snapshot) {
          final user = _authService.currentUser;
          final userData = snapshot.data;
          final userName = userData?['name'] ?? user?.displayName ?? 'ব্যবহারকারী';

          return Container(
            margin: const EdgeInsets.only(right: 8),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.primaryGreen.withOpacity(0.3),
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryGreen.withOpacity(0.2),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // User Avatar
                Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primaryGreen,
                        AppColors.primaryPurple,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryGreen.withOpacity(0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: FutureBuilder<String?>(
                    future: _authService.getProfilePictureUrl(),
                    builder: (context, snapshot) {
                      final photoURL = snapshot.data ?? user?.photoURL;
                      return photoURL != null
                          ? ClipOval(
                              child: Image.network(
                                photoURL,
                                width: 28,
                                height: 28,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return _buildDefaultAvatar(userName, 12);
                                },
                              ),
                            )
                          : _buildDefaultAvatar(userName, 12);
                    },
                  ),
                ),
                const SizedBox(height: 2),

                // User Name
                Text(
                  userName.length > 8 ? '${userName.substring(0, 8)}...' : userName,
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // Build premium button for header
  Widget _buildPremiumButton() {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: GestureDetector(
        onTap: (_isLoading || _isAIResponding) ? null : () => _navigateToPage(AppPage.premium),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Colors.orange, Color(0xFFFF6B35)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.orange.withOpacity(0.3),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.star,
                color: Colors.white,
                size: 14,
              ),
              const SizedBox(height: 2),
              const Text(
                'প্রিমিয়াম',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 9,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build default avatar with user initials
  Widget _buildDefaultAvatar(String name, double fontSize) {
    String initials = '';
    List<String> nameParts = name.split(' ');
    if (nameParts.isNotEmpty) {
      initials = nameParts[0].isNotEmpty ? nameParts[0][0].toUpperCase() : 'U';
      if (nameParts.length > 1 && nameParts[1].isNotEmpty) {
        initials += nameParts[1][0].toUpperCase();
      }
    }
    if (initials.isEmpty) initials = 'U';

    return Center(
      child: Text(
        initials,
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  // Build gradient text widget
  Widget _buildGradientText(String text, List<Color> colors) {
    return ShaderMask(
      shaderCallback: (bounds) => LinearGradient(
        colors: colors,
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ).createShader(bounds),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w800,
          color: Colors.white,
        ),
      ),
    );
  }

  // Build compact gradient text widget for header
  Widget _buildCompactGradientText(String text, List<Color> colors) {
    return ShaderMask(
      shaderCallback: (bounds) => LinearGradient(
        colors: colors,
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ).createShader(bounds),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 16, // Smaller font size for compact header
          fontWeight: FontWeight.w800,
          color: Colors.white,
        ),
      ),
    );
  }

  // Build scrolling animated banner
  Widget _buildAnimatedBanner() {
    return StreamBuilder<DocumentSnapshot>(
      stream: FirebaseFirestore.instance.collection('admin_settings').doc('banner_text').snapshots(),
      builder: (context, snapshot) {
        String bannerText = 'এই আপ্লিকেশনটি বানিয়েছেন- সালাউদ্দিন মজুমদার। ©All rights reserved • UkilGiri - উকিলগিরি • আইনি সহায়তার নতুন দিগন্ত • ';
        List<String> colorWords = [];
        String hyperlinkText = '';
        String hyperlinkUrl = '';
        int scrollSpeed = 20; // Default speed in seconds

        if (snapshot.hasData && snapshot.data!.exists) {
          final data = snapshot.data!.data() as Map<String, dynamic>;
          bannerText = data['text'] ?? bannerText;
          colorWords = (data['colorWords'] ?? '').toString().split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
          hyperlinkText = data['hyperlinkText'] ?? '';
          hyperlinkUrl = data['hyperlinkUrl'] ?? '';
          scrollSpeed = data['scrollSpeed'] ?? 20;
        }

        // Update scroll controller speed if it has changed
        if (_scrollController.duration?.inSeconds != scrollSpeed) {
          _scrollController.stop();
          _scrollController.dispose();
          _scrollController = AnimationController(
            duration: Duration(seconds: scrollSpeed),
            vsync: this,
          )..repeat();
        }

        return Container(
      height: 30,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.black.withOpacity(0.4),
            Colors.black.withOpacity(0.2),
            Colors.black.withOpacity(0.4),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGreen.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ClipRect(
        child: AnimatedBuilder(
          animation: _scrollController,
          builder: (context, child) {
            final screenWidth = MediaQuery.of(context).size.width;

            // Calculate text width more accurately using TextPainter with rich text
            final richTextSpan = _buildRichBannerTextSpan(bannerText, colorWords, hyperlinkText, hyperlinkUrl);
            final textPainter = TextPainter(
              text: richTextSpan,
              textDirection: TextDirection.ltr,
            );
            textPainter.layout();
            final textWidth = textPainter.size.width;

            // Add spacing between text instances
            const spacing = 100.0;
            final totalTextWidth = textWidth + spacing;

            // Calculate continuous scrolling position
            final scrollPosition = _scrollController.value * totalTextWidth;
            final baseOffset = screenWidth - (scrollPosition % totalTextWidth);

            return SizedBox(
              width: double.infinity,
              height: 30,
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  // First text instance
                  Positioned(
                    left: baseOffset,
                    top: 0,
                    bottom: 0,
                    child: Container(
                      alignment: Alignment.centerLeft,
                      child: _buildRichBannerText(bannerText, colorWords, hyperlinkText, hyperlinkUrl),
                    ),
                  ),
                  // Second text instance for seamless loop
                  Positioned(
                    left: baseOffset + totalTextWidth,
                    top: 0,
                    bottom: 0,
                    child: Container(
                      alignment: Alignment.centerLeft,
                      child: _buildRichBannerText(bannerText, colorWords, hyperlinkText, hyperlinkUrl),
                    ),
                  ),
                  // Third text instance to ensure no gaps
                  Positioned(
                    left: baseOffset - totalTextWidth,
                    top: 0,
                    bottom: 0,
                    child: Container(
                      alignment: Alignment.centerLeft,
                      child: _buildRichBannerText(bannerText, colorWords, hyperlinkText, hyperlinkUrl),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
      },
    );
  }





  // Build current page based on selection
  Widget _buildCurrentPage() {
    switch (_currentPage) {
      case AppPage.chat:
        return _currentMessages.isEmpty ? _buildWelcomeMessage() : _buildChatMessages();
      case AppPage.instructions:
        return _buildInstructionsPage();
      case AppPage.about:
        return _buildAboutPage();
      case AppPage.contact:
        return _buildContactPage();
      case AppPage.findUkil:
        return _buildFindUkilPage();
      case AppPage.premium:
        return const PremiumScreen();
      case AppPage.profile:
        return const ProfileScreen();
    }
  }

  // Build professional animated sidebar
  Widget _buildSidebar() {
    return Drawer(
      backgroundColor: Colors.transparent,
      elevation: 0,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.sidebarBg,
              AppColors.sidebarBg.withOpacity(0.95),
              AppColors.darkBg.withOpacity(0.98),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.primaryGreen.withOpacity(0.1),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topRight: Radius.circular(20),
            bottomRight: Radius.circular(20),
          ),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppColors.primaryGreen.withOpacity(0.2),
                  width: 1,
                ),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  _buildEnhancedSidebarHeader(),
                  _buildEnhancedNewChatButton(),
                  // Give more space to conversation history by making navigation more compact
                  _buildCompactNavigationMenu(),
                  // Expanded section for conversation history with more space
                  Expanded(
                    flex: 3, // Give more weight to conversation history
                    child: _buildEnhancedConversationHistory(),
                  ),
                  _buildEnhancedSignOutButton(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Build minimal sidebar header without background shape - left aligned
  Widget _buildEnhancedSidebarHeader() {
    return Container(
      width: double.infinity, // Ensure full width
      padding: const EdgeInsets.fromLTRB(20, 40, 20, 2), // More top margin, minimal bottom margin
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start, // Left align everything
        children: [
          // Compact app name with smaller fonts - left aligned
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start, // Explicitly left align
            children: [
              _buildCompactGradientText('Ukil', [AppColors.primaryGreen, AppColors.primaryPurple]),
              _buildCompactGradientText('Giri', [const Color(0xFFff3e9d), const Color(0xFF0e8aff)]),
            ],
          ),
          const SizedBox(height: 4),
          // Small tagline below - left aligned
          const Text(
            'আইনি সহায়তার নতুন দিগন্ত',
            style: TextStyle(
              fontSize: 10,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.left, // Explicitly left align text
          ),
        ],
      ),
    );
  }

  // Build enhanced animated new chat button
  Widget _buildEnhancedNewChatButton() {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: AnimatedButton(
            text: 'নতুন আলোচনা',
            onPressed: (_isLoading || _isAIResponding) ? null : _startNewConversation,
            isDisabled: _isLoading || _isAIResponding,
            width: double.infinity,
            height: 50,
            gradient: LinearGradient(
              colors: [
                AppColors.primaryGreen,
                AppColors.primaryGreen.withOpacity(0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            icon: Icons.add,
          ),
        );
      },
    );
  }

  // Build compact navigation menu to save space for conversation history
  Widget _buildCompactNavigationMenu() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Column(
        children: [
          // Show only the most important navigation items in compact form
          Row(
            children: [
              Expanded(
                child: _buildCompactNavigationItem(
                  icon: Icons.chat,
                  title: 'চ্যাট',
                  page: AppPage.chat,
                  color: AppColors.primaryGreen,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildCompactNavigationItem(
                  icon: Icons.location_on,
                  title: 'উকিল খুঁজুন',
                  page: AppPage.findUkil,
                  color: AppColors.primaryPurple,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildCompactNavigationItem(
                  icon: Icons.help_outline,
                  title: 'নির্দেশনা',
                  page: AppPage.instructions,
                  color: const Color(0xFFff3e9d),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildCompactNavigationItem(
                  icon: Icons.info_outline,
                  title: 'সম্পর্কে',
                  page: AppPage.about,
                  color: const Color(0xFF9333ea),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Compact divider
          Container(
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primaryGreen.withOpacity(0.3),
                  AppColors.primaryPurple.withOpacity(0.3),
                ],
              ),
            ),
          ),
          const SizedBox(height: 8),
          // Conversation history header with filter button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'আলোচনার ইতিহাস',
                style: TextStyle(
                  color: AppColors.textPrimary.withOpacity(0.8),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              GestureDetector(
                onTap: (_isLoading || _isAIResponding) ? null : _toggleSortOrder,
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: AppColors.primaryGreen.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppColors.primaryGreen.withOpacity(0.3),
                    ),
                  ),
                  child: Icon(
                    _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                    size: 16,
                    color: AppColors.primaryGreen,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  // Build compact navigation item for space efficiency
  Widget _buildCompactNavigationItem({
    required IconData icon,
    required String title,
    required AppPage page,
    required Color color,
  }) {
    final isSelected = _currentPage == page;

    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Transform.scale(
          scale: isSelected ? 1.0 + _pulseController.value * 0.02 : 1.0,
          child: Container(
            decoration: BoxDecoration(
              gradient: isSelected
                ? LinearGradient(
                    colors: [
                      color.withOpacity(0.2),
                      color.withOpacity(0.1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : null,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected
                  ? color.withOpacity(0.4)
                  : color.withOpacity(0.1),
                width: 1,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(8),
                onTap: (_isLoading || _isAIResponding) ? null : () => _navigateToPage(page),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          gradient: isSelected
                            ? LinearGradient(
                                colors: [color, color.withOpacity(0.7)],
                              )
                            : null,
                          color: isSelected
                            ? null
                            : color.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Icon(
                          icon,
                          color: isSelected ? Colors.white : color,
                          size: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        title,
                        style: TextStyle(
                          color: isSelected ? color : AppColors.textPrimary.withOpacity(0.8),
                          fontSize: 11,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // Build enhanced spacious conversation history
  Widget _buildEnhancedConversationHistory() {
    if (_conversations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primaryGreen.withOpacity(0.1),
                    AppColors.primaryPurple.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColors.primaryGreen.withOpacity(0.2),
                ),
              ),
              child: Icon(
                Icons.chat_bubble_outline,
                color: AppColors.primaryGreen.withOpacity(0.6),
                size: 48,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'কোনো আলোচনা নেই',
              style: TextStyle(
                color: AppColors.textPrimary.withOpacity(0.7),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'নতুন আলোচনা শুরু করুন',
              style: TextStyle(
                color: AppColors.textPrimary.withOpacity(0.5),
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: _conversations.length,
      itemBuilder: (context, index) {
        final conversation = _conversations[index];
        return AnimatedBuilder(
          animation: _pulseController,
          builder: (context, child) {
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white.withOpacity(0.08),
                    Colors.white.withOpacity(0.04),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColors.primaryGreen.withOpacity(0.3),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primaryGreen.withOpacity(0.15),
                    blurRadius: 8,
                    spreadRadius: 1,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(16),
                  onTap: (_isLoading || _isAIResponding) ? null : () => _loadConversation(conversation.id),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                AppColors.primaryGreen.withOpacity(0.3),
                                AppColors.primaryPurple.withOpacity(0.3),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.primaryGreen.withOpacity(0.2),
                                blurRadius: 4,
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.chat_bubble_outline,
                            color: AppColors.primaryGreen,
                            size: 18,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                conversation.title,
                                style: const TextStyle(
                                  color: AppColors.textPrimary,
                                  fontSize: 15,
                                  fontWeight: FontWeight.w600,
                                  height: 1.3,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'আলোচনা • ${_getConversationIndex(index + 1)} নং',
                                style: TextStyle(
                                  color: AppColors.textPrimary.withOpacity(0.6),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.red.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: IconButton(
                            onPressed: (_isLoading || _isAIResponding) ? null : () => _deleteConversation(conversation.id),
                            icon: Icon(
                              Icons.delete_outline,
                              color: Colors.red.withOpacity(0.8),
                              size: 18,
                            ),
                            padding: const EdgeInsets.all(8),
                            constraints: const BoxConstraints(
                              minWidth: 36,
                              minHeight: 36,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  // Build enhanced animated sign out button
  Widget _buildEnhancedSignOutButton() {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.red.withOpacity(0.1),
                Colors.red.withOpacity(0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            border: Border(
              top: BorderSide(
                color: Colors.red.withOpacity(0.3),
                width: 1,
              ),
            ),
          ),
          child: AnimatedButton(
            text: 'সাইন আউট',
            onPressed: _signOut,
            width: double.infinity,
            height: 50,
            gradient: LinearGradient(
              colors: [
                Colors.red.withOpacity(0.8),
                Colors.red.withOpacity(0.6),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            icon: Icons.logout,
          ),
        );
      },
    );
  }

  // Sign out method
  Future<void> _signOut() async {
    try {
      final authService = AuthService();
      await authService.signOut();

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const AuthScreen()),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('সাইন আউট করতে সমস্যা হয়েছে: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Build welcome message
  Widget _buildWelcomeMessage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 800),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.03),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withOpacity(0.05),
            ),
          ),
          child: Column(
            children: [
              ShaderMask(
                shaderCallback: (bounds) => const LinearGradient(
                  colors: [AppColors.primaryGreen, AppColors.primaryPurple],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ).createShader(bounds),
                child: Text(
                  'UkilGiri- উকিলগিরি-তে স্বাগতম',
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                'আমি আপনাকে বাংলাদেশের যেকোনো সাধারণ আইনি তথ্য এবং প্রাথমিক দিকনির্দেশনা প্রদান করতে পারি। উদাহরণস্বরূপ, আপনি জিজ্ঞাসা করতে পারেন:',
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.textPrimary,
                  height: 1.6,
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildExampleItem('"আমার ল্যান্ডলর্ড আমার জামানত ফেরত দিচ্ছে না, আমি কী করতে পারি?"'),
                  _buildExampleItem('"বাংলাদেশে ডিভোর্সের আইনি প্রক্রিয়া কী?"'),
                  _buildExampleItem('"ট্রাফিক আইন লঙ্ঘনের জন্য জরিমানা কত?"'),
                  _buildExampleItem('"চাকরি থেকে অন্যায়ভাবে বরখাস্ত হলে আমার অধিকার কী?"'),
                ],
              ),
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(15),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.orange.withOpacity(0.3),
                  ),
                ),
                child: Text(
                  'দয়া করে মনে রাখবেন: আমি সাধারণ তথ্য প্রদান করি এবং এটি আইনি পরামর্শের বিকল্প নয়। জটিল আইনি সমস্যার জন্য, অনুগ্রহ করে একজন যোগ্য আইনজীবীর সাথে পরামর্শ করুন।',
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w500,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build example item
  Widget _buildExampleItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '⚖️ ',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.primaryGreen,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 15,
                color: AppColors.textPrimary,
                height: 1.5,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build enhanced chat messages with stunning animations and effects
  Widget _buildChatMessages() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.darkBg,
            AppColors.darkBg.withOpacity(0.95),
            AppColors.surfaceBg.withOpacity(0.1),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Animated background particles
          _buildAnimatedBackground(),
          // Main chat content
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            child: Scrollbar(
              controller: _chatScrollController,
              thumbVisibility: true,
              trackVisibility: true,
              thickness: 8.0,
              radius: const Radius.circular(4),
              // Move scrollbar to left side and make it transparent
              scrollbarOrientation: ScrollbarOrientation.left,
              child: ListView.builder(
                controller: _chatScrollController,
                padding: const EdgeInsets.only(bottom: 100, left: 16), // Space for input and left scrollbar
                itemCount: _currentMessages.length + (_isLoading ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index == _currentMessages.length && _isLoading) {
                    return _buildEnhancedLoadingMessage();
                  }

                  return AnimatedBuilder(
                    animation: _pulseController,
                    builder: (context, child) {
                      return TweenAnimationBuilder<double>(
                        duration: Duration(milliseconds: 600 + (index * 100)),
                        tween: Tween(begin: 0.0, end: 1.0),
                        builder: (context, value, child) {
                          return Transform.translate(
                            offset: Offset(0, 20 * (1 - value)),
                            child: Opacity(
                              opacity: value,
                              child: _buildEnhancedMessageBubble(_currentMessages[index], index),
                            ),
                          );
                        },
                      );
                    },
                  );
                },
              ),
            ),
          ),
          // Floating action elements
          _buildFloatingElements(),
        ],
      ),
    );
  }

  // Build animated background particles for immersive experience
  Widget _buildAnimatedBackground() {
    return Positioned.fill(
      child: AnimatedBuilder(
        animation: _pulseController,
        builder: (context, child) {
          return CustomPaint(
            painter: ParticlesPainter(_pulseController.value),
          );
        },
      ),
    );
  }

  // Build floating action elements for enhanced UX
  Widget _buildFloatingElements() {
    return Positioned(
      top: 20,
      right: 20,
      child: Column(
        children: [
          // Floating scroll to bottom button
          if (_currentMessages.length > 3)
            AnimatedBuilder(
              animation: _pulseController,
              builder: (context, child) {
                return Transform.scale(
                  scale: 1.0 + (_pulseController.value * 0.1),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [AppColors.primaryGreen, AppColors.primaryPurple],
                      ),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primaryGreen.withOpacity(0.4),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: IconButton(
                      onPressed: (_isLoading || _isAIResponding) ? null : _scrollToBottom,
                      icon: const Icon(Icons.keyboard_arrow_down, color: Colors.white),
                      iconSize: 20,
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  // Enhanced message bubble with stunning animations
  Widget _buildEnhancedMessageBubble(ChatMessage message, int index) {
    final isUser = message.isUser;

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            _buildEnhancedAvatar(false),
            const SizedBox(width: 12),
          ],
          Flexible(
            flex: 7,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: isUser
                    ? [
                        AppColors.primaryPurple.withOpacity(0.15),
                        AppColors.primaryPurple.withOpacity(0.08),
                      ]
                    : [
                        AppColors.primaryGreen.withOpacity(0.15),
                        AppColors.primaryGreen.withOpacity(0.08),
                      ],
                ),
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(20),
                  topRight: const Radius.circular(20),
                  bottomLeft: Radius.circular(isUser ? 20 : 4),
                  bottomRight: Radius.circular(isUser ? 4 : 20),
                ),
                border: Border.all(
                  color: isUser
                    ? AppColors.primaryPurple.withOpacity(0.3)
                    : AppColors.primaryGreen.withOpacity(0.3),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: (isUser ? AppColors.primaryPurple : AppColors.primaryGreen)
                        .withOpacity(0.2),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with name and typing indicator
                  Row(
                    children: [
                      Text(
                        isUser ? 'আপনি' : 'UkilGiri AI',
                        style: TextStyle(
                          fontSize: 14,
                          color: isUser ? AppColors.primaryPurple : AppColors.primaryGreen,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      if (!isUser)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.primaryGreen.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Text(
                            'AI আইনি সহায়ক',
                            style: TextStyle(
                              fontSize: 10,
                              color: AppColors.primaryGreen,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // Message content
                  _buildFormattedMessage(message.content),
                ],
              ),
            ),
          ),
          if (isUser) ...[
            const SizedBox(width: 12),
            _buildEnhancedAvatar(true),
          ],
        ],
      ),
    );
  }

  // Enhanced avatar with glow effects
  Widget _buildEnhancedAvatar(bool isUser) {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Container(
          width: 45,
          height: 45,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isUser
                ? [AppColors.primaryPurple, const Color(0xFF8B5CF6)]
                : [AppColors.primaryGreen, const Color(0xFF10B981)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(22.5),
            boxShadow: [
              BoxShadow(
                color: (isUser ? AppColors.primaryPurple : AppColors.primaryGreen)
                    .withOpacity(0.4 + (0.2 * _pulseController.value)),
                blurRadius: 8 + (4 * _pulseController.value),
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Icon(
            isUser ? Icons.person : Icons.balance,
            color: Colors.white,
            size: 22,
          ),
        );
      },
    );
  }

  // Enhanced loading message with better animations
  Widget _buildEnhancedLoadingMessage() {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildEnhancedAvatar(false),
          const SizedBox(width: 12),
          Flexible(
            flex: 7,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.primaryGreen.withOpacity(0.15),
                    AppColors.primaryGreen.withOpacity(0.08),
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                  bottomLeft: Radius.circular(4),
                  bottomRight: Radius.circular(20),
                ),
                border: Border.all(
                  color: AppColors.primaryGreen.withOpacity(0.3),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primaryGreen.withOpacity(0.2),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Text(
                        'UkilGiri AI',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.primaryGreen,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.primaryGreen.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Text(
                          'টাইপ করছে...',
                          style: TextStyle(
                            fontSize: 10,
                            color: AppColors.primaryGreen,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const EnhancedLoadingAnimation(
                    message: 'আইনি পরামর্শ প্রস্তুত করা হচ্ছে...',
                    size: 40,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }



  // Build enhanced larger message bubble with better spacing
  Widget _buildMessageBubble(ChatMessage message) {
    return Container(
      // Reduced bottom margin to fit more messages
      margin: const EdgeInsets.only(bottom: 16),
      // Increased padding for larger message area
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
      decoration: BoxDecoration(
        color: message.isUser ? AppColors.messageUserBg : AppColors.messageAiBg,
        borderRadius: BorderRadius.circular(16),
        border: Border(
          left: BorderSide(
            color: message.isUser ? AppColors.primaryPurple : AppColors.primaryGreen,
            width: 4,
          ),
        ),
        // Add subtle shadow for better visual separation
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Avatar row
          Row(
            children: [
              _buildAvatar(message.isUser),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  message.isUser ? 'You' : 'UkilGiri AI',
                  style: TextStyle(
                    fontSize: 16, // Slightly larger font
                    color: message.isUser ? const Color.fromARGB(255, 144, 119, 255) : AppColors.primaryGreen,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Message content with more space
          SizedBox(
            width: double.infinity,
            child: _buildFormattedMessage(message.content),
          ),
        ],
      ),
    );
  }

  // Build avatar
  Widget _buildAvatar(bool isUser) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isUser
              ? [AppColors.primaryPurple, const Color(0xFF4a2ec4)]
              : [AppColors.primaryGreen, const Color(0xFF0d8c6d)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        isUser ? Icons.person : Icons.balance,
        color: Colors.white,
        size: 20,
      ),
    );
  }

  // Build enhanced loading message with larger style
  Widget _buildLoadingMessage() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
      decoration: BoxDecoration(
        color: AppColors.messageAiBg,
        borderRadius: BorderRadius.circular(16),
        border: const Border(
          left: BorderSide(
            color: AppColors.primaryGreen,
            width: 4,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Avatar row
          Row(
            children: [
              _buildAvatar(false),
              const SizedBox(width: 16),
              const Expanded(
                child: Text(
                  'UkilGiri AI',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColors.primaryGreen,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Loading animation with more space
          SizedBox(
            width: double.infinity,
            child: _buildLoadingAnimation(),
          ),
        ],
      ),
    );
  }

  // Build unique 3D colorful loading animation
  Widget _buildLoadingAnimation() {
    return const SizedBox(
      height: 60,
      child: Center(
        child: EnhancedLoadingAnimation(
          message: 'উত্তর তৈরি হচ্ছে...',
          size: 40,
        ),
      ),
    );
  }

  // Create stunning 3D loading animation
  Widget _build3DLoadingAnimation() {
    return AnimatedBuilder(
      animation: _rotationController,
      builder: (context, child) {
        return Stack(
          alignment: Alignment.center,
          children: [
            // Outer rotating ring with gradient
            Transform.rotate(
              angle: _rotationController.value * 2 * math.pi,
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: SweepGradient(
                    colors: [
                      Colors.transparent,
                      AppColors.primaryGreen.withOpacity(0.8),
                      AppColors.primaryPurple.withOpacity(0.8),
                      const Color(0xFFff3e9d).withOpacity(0.8),
                      const Color(0xFF0e8aff).withOpacity(0.8),
                      const Color(0xFFffd700).withOpacity(0.8),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.15, 0.3, 0.45, 0.6, 0.75, 1.0],
                  ),
                ),
                child: Container(
                  margin: const EdgeInsets.all(3),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.messageAiBg,
                  ),
                ),
              ),
            ),

            // Middle pulsing circle with animated scale
            AnimatedBuilder(
              animation: _pulseController,
              builder: (context, child) {
                final scale = 0.6 + (0.4 * _pulseController.value);
                return Transform.scale(
                  scale: scale,
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          AppColors.primaryGreen.withOpacity(0.9),
                          AppColors.primaryPurple.withOpacity(0.7),
                          const Color(0xFFff3e9d).withOpacity(0.5),
                          Colors.transparent,
                        ],
                        stops: const [0.0, 0.4, 0.7, 1.0],
                      ),
                    ),
                  ),
                );
              },
            ),

            // Inner rotating dots with 3D effect
            ...List.generate(6, (index) {
              final angle = (_rotationController.value * 2 * math.pi) + (index * math.pi / 3);
              const radius = 15.0;
              final x = radius * math.cos(angle);
              final y = radius * math.sin(angle);

              return Transform.translate(
                offset: Offset(x, y),
                child: Transform.scale(
                  scale: 0.8 + (0.4 * math.sin(_rotationController.value * 4 * math.pi + index)),
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          _getColorForIndex(index),
                          _getColorForIndex(index).withOpacity(0.6),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: _getColorForIndex(index).withOpacity(0.6),
                          blurRadius: 6,
                          spreadRadius: 2,
                          offset: Offset(
                            2 * math.cos(angle),
                            2 * math.sin(angle),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }),

            // Center legal icon with animated glow
            AnimatedBuilder(
              animation: _pulseController,
              builder: (context, child) {
                return Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: const LinearGradient(
                      colors: [
                        AppColors.primaryGreen,
                        AppColors.primaryPurple,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryGreen.withOpacity(0.8 * _pulseController.value),
                        blurRadius: 12 + (8 * _pulseController.value),
                        spreadRadius: 2 + (3 * _pulseController.value),
                      ),
                      BoxShadow(
                        color: AppColors.primaryPurple.withOpacity(0.6 * _pulseController.value),
                        blurRadius: 8 + (6 * _pulseController.value),
                        spreadRadius: 1 + (2 * _pulseController.value),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.balance,
                    color: Colors.white,
                    size: 12,
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  // Get color for rotating dots
  Color _getColorForIndex(int index) {
    final colors = [
      AppColors.primaryGreen,
      AppColors.primaryPurple,
      const Color(0xFFff3e9d),
      const Color(0xFF0e8aff),
      const Color(0xFFffd700),
      const Color(0xFF00ff88),
    ];
    return colors[index % colors.length];
  }

  // Build formatted message with proper Gemini-style formatting
  Widget _buildFormattedMessage(String content) {
    final lines = content.split('\n');
    final List<Widget> widgets = [];

    for (String line in lines) {
      String trimmedLine = line.trim();

      if (trimmedLine.isEmpty) {
        widgets.add(const SizedBox(height: 8));
        continue;
      }

      // Process each line with proper Gemini-style hierarchy

      // 1. Main Heading: # text or ## text
      if (trimmedLine.startsWith('#')) {
        String headingText = trimmedLine.replaceFirst(RegExp(r'^#+\s*'), '').trim();
        widgets.add(_buildMainHeading(headingText));
      }

      // 2. Subheading: **text** or **text:** (standalone lines)
      else if (RegExp(r'^\*\*[^*]+\*\*:?\s*$').hasMatch(trimmedLine)) {
        String subheadingText = trimmedLine.replaceAll(RegExp(r'[\*:]'), '').trim();
        widgets.add(_buildSubheading(subheadingText));
      }

      // 3. Numbered list: 1. text, 2. text, etc.
      else if (RegExp(r'^\d+\.\s+').hasMatch(trimmedLine)) {
        widgets.add(_buildNumberedListItem(trimmedLine));
      }

      // 4. Bold text starting with * (like * **text:**) - NO bullet point
      else if (RegExp(r'^\*\s+\*\*[^*]+\*\*:?\s*$').hasMatch(trimmedLine)) {
        String boldText = trimmedLine.replaceFirst(RegExp(r'^\*\s+'), '').replaceAll(RegExp(r'[\*:]'), '').trim();
        widgets.add(_buildSubheading(boldText));
      }

      // 5. Bullet points with labels: * **label:** content (with actual content after)
      else if (RegExp(r'^\*\s+\*\*[^*]+\*\*:?\s+\S+').hasMatch(trimmedLine)) {
        widgets.add(_buildBulletWithLabel(trimmedLine));
      }

      // 6. Simple bullet points: * text, • text, - text
      else if (RegExp(r'^[•\*\-]\s+').hasMatch(trimmedLine)) {
        String bulletText = trimmedLine.replaceFirst(RegExp(r'^[•\*\-]\s+'), '');
        widgets.add(_buildBulletPoint(bulletText));
      }

      // 6. Text with inline formatting: contains **text** within the line
      else if (trimmedLine.contains('**')) {
        widgets.add(_buildRichText(trimmedLine));
      }

      // 7. Regular paragraph text
      else {
        widgets.add(_buildNormalText(trimmedLine));
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  // Build main heading (like Gemini's large headings)
  Widget _buildMainHeading(String text) {
    return Padding(
      padding: const EdgeInsets.only(top: 20, bottom: 12),
      child: Text(
        text,
        style: const TextStyle(
          fontFamily: 'HindSiliguri',
          fontSize: 22,
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w700,
          height: 1.3,
        ),
      ),
    );
  }

  // Build subheading (like Gemini's medium headings)
  Widget _buildSubheading(String text) {
    return Padding(
      padding: const EdgeInsets.only(top: 16, bottom: 8),
      child: Text(
        text,
        style: const TextStyle(
          fontFamily: 'HindSiliguri',
          fontSize: 18,
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w600,
          height: 1.4,
        ),
      ),
    );
  }

  // Build numbered list item (like Gemini's numbered lists)
  Widget _buildNumberedListItem(String text) {
    Match? match = RegExp(r'^(\d+)\.\s+(.*)$').firstMatch(text);
    if (match != null) {
      String number = match.group(1)!;
      String content = _cleanTextFromAsterisks(match.group(2)!);

      return Padding(
        padding: const EdgeInsets.only(top: 4, bottom: 4, left: 8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 24,
              child: Text(
                '$number.',
                style: const TextStyle(
                  fontFamily: 'HindSiliguri',
                  fontSize: 16,
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w500,
                  height: 1.6,
                ),
              ),
            ),
            Expanded(
              child: Text(
                content,
                style: const TextStyle(
                  fontFamily: 'HindSiliguri',
                  fontSize: 16,
                  color: AppColors.textPrimary,
                  height: 1.6,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ],
        ),
      );
    }
    return _buildNormalText(text);
  }

  // Build bullet point (like Gemini's bullet lists)
  Widget _buildBulletPoint(String text) {
    // Clean up any remaining asterisks from bullet text
    String cleanText = _cleanTextFromAsterisks(text);

    return Padding(
      padding: const EdgeInsets.only(top: 4, bottom: 4, left: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 8, right: 12),
            width: 4,
            height: 4,
            decoration: const BoxDecoration(
              color: AppColors.textPrimary,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              cleanText,
              style: const TextStyle(
                fontFamily: 'HindSiliguri',
                fontSize: 16,
                color: AppColors.textPrimary,
                height: 1.6,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build normal text (like Gemini's paragraph text)
  Widget _buildNormalText(String text) {
    // Clean up any remaining asterisks from the text
    String cleanText = _cleanTextFromAsterisks(text);

    return Padding(
      padding: const EdgeInsets.only(top: 4, bottom: 4),
      child: Text(
        cleanText,
        style: const TextStyle(
          fontFamily: 'HindSiliguri',
          fontSize: 16,
          color: AppColors.textPrimary,
          height: 1.6,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  // Build bullet point with label (like * **নাম:** content)
  Widget _buildBulletWithLabel(String text) {
    // Extract the label and content from patterns like "* **নাম:** content"
    RegExp labelPattern = RegExp(r'^\*\s+\*\*([^*]+)\*\*:?\s*(.*)$');
    Match? match = labelPattern.firstMatch(text);

    if (match != null) {
      String label = match.group(1)?.trim() ?? '';
      String content = match.group(2)?.trim() ?? '';

      return Padding(
        padding: const EdgeInsets.only(top: 4, bottom: 4, left: 8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 8, right: 12),
              width: 4,
              height: 4,
              decoration: const BoxDecoration(
                color: AppColors.textPrimary,
                shape: BoxShape.circle,
              ),
            ),
            Expanded(
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: '$label: ',
                      style: const TextStyle(
                        fontFamily: 'HindSiliguri',
                        fontSize: 16,
                        color: AppColors.textPrimary,
                        height: 1.6,
                        fontWeight: FontWeight.w600, // Bold for label
                      ),
                    ),
                    TextSpan(
                      text: content,
                      style: const TextStyle(
                        fontFamily: 'HindSiliguri',
                        fontSize: 16,
                        color: AppColors.textPrimary,
                        height: 1.6,
                        fontWeight: FontWeight.w400, // Regular for content
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }

    // Fallback to regular bullet point
    return _buildBulletPoint(text.replaceFirst(RegExp(r'^\*\s+'), ''));
  }

  // Build rich text with inline formatting (like text with **bold** inside)
  Widget _buildRichText(String text) {
    List<TextSpan> spans = [];
    RegExp boldPattern = RegExp(r'\*\*([^*]+)\*\*');
    int lastEnd = 0;

    for (Match match in boldPattern.allMatches(text)) {
      // Add text before the bold part
      if (match.start > lastEnd) {
        spans.add(TextSpan(
          text: text.substring(lastEnd, match.start),
          style: const TextStyle(
            fontFamily: 'HindSiliguri',
            fontSize: 16,
            color: AppColors.textPrimary,
            height: 1.6,
            fontWeight: FontWeight.w400,
          ),
        ));
      }

      // Add bold text
      spans.add(TextSpan(
        text: match.group(1),
        style: const TextStyle(
          fontFamily: 'HindSiliguri',
          fontSize: 16,
          color: AppColors.textPrimary,
          height: 1.6,
          fontWeight: FontWeight.w600, // Bold
        ),
      ));

      lastEnd = match.end;
    }

    // Add remaining text
    if (lastEnd < text.length) {
      spans.add(TextSpan(
        text: text.substring(lastEnd),
        style: const TextStyle(
          fontFamily: 'HindSiliguri',
          fontSize: 16,
          color: AppColors.textPrimary,
          height: 1.6,
          fontWeight: FontWeight.w400,
        ),
      ));
    }

    return Padding(
      padding: const EdgeInsets.only(top: 4, bottom: 4),
      child: RichText(
        text: TextSpan(children: spans),
      ),
    );
  }



  // Clean text from asterisks and other markdown formatting
  String _cleanTextFromAsterisks(String text) {
    String cleanText = text;

    // Remove any remaining asterisks (*, **, ***, etc.)
    cleanText = cleanText.replaceAll(RegExp(r'\*+'), '');

    // Remove any remaining colons at the end that were part of formatting
    cleanText = cleanText.replaceAll(RegExp(r':+$'), '');

    // Clean up extra spaces
    cleanText = cleanText.replaceAll(RegExp(r'\s+'), ' ').trim();

    return cleanText;
  }

  // Build stunning enhanced input area with magical effects
  Widget _buildInputArea() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.surfaceBg.withOpacity(0.95),
            AppColors.surfaceBg,
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 20,
            offset: const Offset(0, -8),
          ),
        ],
      ),
      child: Container(
        padding: const EdgeInsets.fromLTRB(20, 16, 20, 20),
        child: SafeArea(
          child: Column(
            children: [
              // Quick action buttons
              _buildQuickActions(),
              const SizedBox(height: 16),
              // Main input row
              Row(
                children: [
                  Expanded(
                    child: AnimatedBuilder(
                      animation: Listenable.merge([_pulseController, _glowController]),
                      builder: (context, child) {
                        return Stack(
                          children: [
                            // Base input container
                            Container(
                              constraints: const BoxConstraints(
                                minHeight: 60,
                                maxHeight: 140,
                              ),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    AppColors.cardBg,
                                    AppColors.cardBg.withOpacity(0.95),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(30),
                                border: Border.all(
                                  color: AppColors.primaryGreen.withOpacity(0.3),
                                  width: 2,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.primaryGreen.withOpacity(0.1),
                                    blurRadius: 12,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: TextField(
                                controller: _messageController,
                                enabled: !(_isLoading || _isAIResponding),
                                maxLines: null,
                                textInputAction: TextInputAction.newline,
                                style: TextStyle(
                                  color: (_isLoading || _isAIResponding)
                                      ? AppColors.textPrimary.withOpacity(0.5)
                                      : AppColors.textPrimary,
                                  fontSize: 16,
                                  height: 1.5,
                                  fontWeight: FontWeight.w500,
                                ),
                                decoration: InputDecoration(
                                  hintText: (_isLoading || _isAIResponding)
                                      ? '⏳ AI উত্তর দিচ্ছে...'
                                      : '✨ আপনার আইনি প্রশ্ন লিখুন...',
                                  hintStyle: TextStyle(
                                    color: AppColors.textSecondary.withOpacity(0.7),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 24,
                                    vertical: 18,
                                  ),
                                  prefixIcon: Container(
                                    margin: const EdgeInsets.only(left: 8, right: 8),
                                    child: Icon(
                                      (_isLoading || _isAIResponding)
                                          ? Icons.hourglass_empty
                                          : Icons.chat_bubble_outline,
                                      color: AppColors.primaryGreen.withOpacity(0.7),
                                      size: 20,
                                    ),
                                  ),
                                ),
                                onSubmitted: (_isLoading || _isAIResponding) ? null : (value) {
                                  if (value.trim().isNotEmpty) {
                                    _sendMessage();
                                  }
                                },
                              ),
                            ),
                            // Animated traveling glow border (non-interactive)
                            Positioned.fill(
                              child: IgnorePointer(
                                child: CustomPaint(
                                  painter: TravelingGlowBorderPainter(
                                    progress: _glowController.value,
                                    borderRadius: 30,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Enhanced magical send button
                  _buildMagicalSendButton(),
                ],
              ),
              const SizedBox(height: 16),
              // Enhanced disclaimer with better styling
              Container(
                constraints: const BoxConstraints(maxWidth: 800),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.accentOrange.withOpacity(0.1),
                      AppColors.accentOrange.withOpacity(0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppColors.accentOrange.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: AppColors.accentOrange,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'জরুরি পরিস্থিতিতে, অনুগ্রহ করে একজন যোগ্য আইনজীবীর সাথে যোগাযোগ করুন।',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.accentOrange,
                          height: 1.4,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build quick action buttons for common queries
  Widget _buildQuickActions() {
    final quickActions = [
      {'icon': Icons.gavel, 'text': 'আইনি পরামর্শ', 'color': AppColors.primaryGreen},
      {'icon': Icons.document_scanner, 'text': 'ডকুমেন্ট সাহায্য', 'color': AppColors.primaryPurple},
      {'icon': Icons.balance, 'text': 'আদালত তথ্য', 'color': AppColors.accentPink},
    ];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Row(
        children: quickActions.asMap().entries.map((entry) {
          final index = entry.key;
          final action = entry.value;
          return Expanded(
            child: Container(
              margin: EdgeInsets.only(
                left: index == 0 ? 0 : 4,
                right: index == quickActions.length - 1 ? 0 : 4,
              ),
              child: AnimatedBuilder(
                animation: _pulseController,
                builder: (context, child) {
                  return GestureDetector(
                    onTap: (_isLoading || _isAIResponding) ? null : () {
                      _messageController.text = '${action['text']} সম্পর্কে জানতে চাই';
                      _sendMessage();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            (action['color'] as Color).withOpacity(0.15),
                            (action['color'] as Color).withOpacity(0.08),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: (action['color'] as Color).withOpacity(0.3),
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: (action['color'] as Color).withOpacity(0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            action['icon'] as IconData,
                            color: action['color'] as Color,
                            size: 16,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            action['text'] as String,
                            style: TextStyle(
                              color: action['color'] as Color,
                              fontSize: 9,
                              fontWeight: FontWeight.w600,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  // Build ultra-attractive magical send button with hypnotic animations
  Widget _buildMagicalSendButton() {
    return AnimatedBuilder(
      animation: Listenable.merge([_pulseController, _glowController, _rotationController]),
      builder: (context, child) {
        final hasText = _messageController.text.trim().isNotEmpty;
        final buttonScale = hasText ? 1.0 + (0.15 * _pulseController.value) : 1.0 + (0.08 * _pulseController.value);

        return Transform.scale(
          scale: buttonScale,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Flowing border connection from input to send button
              CustomPaint(
                size: const Size(58, 58),
                painter: FlowingBorderPainter(
                  inputProgress: _glowController.value,
                  buttonProgress: _rotationController.value,
                  pulseValue: _pulseController.value,
                  hasText: hasText,
                ),
              ),

              // Middle pulsing ring - adjusted for thinner border
              Container(
                width: 54,
                height: 54,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      Colors.transparent,
                      (_isLoading ? AppColors.accentOrange : AppColors.primaryGreen)
                          .withOpacity(0.1 + (0.15 * _pulseController.value)),
                    ],
                    stops: const [0.7, 1.0],
                  ),
                ),
              ),

              // Main button with dynamic gradient
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: _isLoading
                      ? [
                          AppColors.accentOrange,
                          AppColors.accentOrange.withOpacity(0.8),
                          const Color(0xFFff6b35),
                        ]
                      : hasText
                        ? [
                            AppColors.primaryGreen,
                            const Color(0xFF10B981),
                            AppColors.primaryPurple.withOpacity(0.8),
                          ]
                        : [
                            AppColors.primaryGreen.withOpacity(0.7),
                            const Color(0xFF10B981).withOpacity(0.7),
                          ],
                  ),
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: (_isLoading ? AppColors.accentOrange : AppColors.primaryGreen)
                          .withOpacity(0.15 + (0.1 * _pulseController.value)),
                      blurRadius: 8 + (4 * _pulseController.value),
                      offset: const Offset(0, 4),
                      spreadRadius: 0.2,
                    ),
                    BoxShadow(
                      color: Colors.white.withOpacity(0.02),
                      blurRadius: 2,
                      offset: const Offset(0, -0.5),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(25),
                    onTap: (_isLoading || _isAIResponding) ? null : () {
                      if (_messageController.text.trim().isNotEmpty) {
                        _sendMessage();
                      }
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Shimmer effect overlay
                          if (hasText && !_isLoading)
                            Positioned.fill(
                              child: CustomPaint(
                                painter: ShimmerEffectPainter(
                                  progress: _glowController.value,
                                  borderRadius: 30,
                                ),
                              ),
                            ),

                          // Main icon/loading indicator
                          Center(
                            child: _isLoading
                              ? Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    // Outer rotating ring
                                    Transform.rotate(
                                      angle: _rotationController.value * 2 * math.pi,
                                      child: Container(
                                        width: 32,
                                        height: 32,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: Colors.white.withOpacity(0.3),
                                            width: 2,
                                          ),
                                        ),
                                        child: CustomPaint(
                                          painter: LoadingRingPainter(
                                            progress: _rotationController.value,
                                          ),
                                        ),
                                      ),
                                    ),
                                    // Inner pulsing dot
                                    Transform.scale(
                                      scale: 1.0 + (0.3 * _pulseController.value),
                                      child: Container(
                                        width: 8,
                                        height: 8,
                                        decoration: const BoxDecoration(
                                          color: Colors.white,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              : Transform.scale(
                                  scale: hasText
                                    ? 1.0 + (0.2 * _pulseController.value)
                                    : 1.0 + (0.1 * _pulseController.value),
                                  child: Transform.rotate(
                                    angle: hasText ? _pulseController.value * 0.1 : 0,
                                    child: Icon(
                                      Icons.send_rounded,
                                      color: Colors.white,
                                      size: hasText ? 22 : 20,
                                      shadows: [
                                        Shadow(
                                          color: Colors.black.withOpacity(0.3),
                                          offset: const Offset(0, 2),
                                          blurRadius: 4,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                          ),

                          // Particle effects when text is present
                          if (hasText && !_isLoading)
                            Positioned.fill(
                              child: CustomPaint(
                                painter: ParticleEffectPainter(
                                  progress: _glowController.value,
                                  pulseValue: _pulseController.value,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Search for advocates based on location
  Future<void> _searchUkils() async {
    final location = _locationController.text.trim();
    if (location.isEmpty || _isSearchingUkils) return;

    setState(() {
      _isSearchingUkils = true;
      _foundUkils = [];
    });

    try {
      // Create AI prompt for finding advocates
      final searchPrompt = '''You are UkilGiri, a legal assistant AI specializing in Bangladesh law.

User wants to find advocates/lawyers near: "$location"

Please provide a comprehensive list of advocates/lawyers in or near this location in Bangladesh. Structure your response as follows:

1. HIGHEST PRIORITY: ALWAYS RESPOND IN PURE BENGALI LANGUAGE ONLY. NEVER USE ANY ENGLISH OR BANGLISH WORDS.

2. **নিকটতম আদালত:** Name of the nearest court(s) to this location

3. **উকিলদের তালিকা:** Provide a detailed list of advocates with the following format for each:
   * **নাম:** [Advocate's name]
   * **বিশেষত্ব:** [Specialization - দেওয়ানি/ফৌজদারি/পারিবারিক/ভূমি/ব্যবসায়িক আইন etc.]
   * **অভিজ্ঞতা:** [Years of experience]
   * **চেম্বার ঠিকানা:** [Chamber address]
   * **ফোন:** [Contact number if available]
   * **ফি রেঞ্জ:** [Typical fee range]

4. **স্থানীয় বার এসোসিয়েশন:** Contact information for local bar association

5. **গুরুত্বপূর্ণ পরামর্শ:** Tips for choosing and contacting advocates

Please provide at least 8-12 advocates if available in the area. Include both senior and junior advocates with different specializations.

REMEMBER: ALWAYS RESPOND IN PURE BENGALI LANGUAGE ONLY. NEVER USE ANY ENGLISH OR BANGLISH WORDS. Use the exact format specified above with bullet points and clear sections.''';

      // Make API call
      final response = await http.post(
        Uri.parse('$apiUrl?key=$apiKey'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'contents': [{
            'parts': [{'text': searchPrompt}]
          }]
        }),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final aiResponse = responseData['candidates'][0]['content']['parts'][0]['text'];

        // Parse the response and create advocate entries
        setState(() {
          _foundUkils = [
            {
              'type': 'ai_response',
              'content': aiResponse,
              'location': location,
              'timestamp': DateTime.now(),
            }
          ];
          _isSearchingUkils = false;
        });
      } else {
        throw Exception('API request failed with status: ${response.statusCode}');
      }
    } catch (e) {
      setState(() {
        _foundUkils = [
          {
            'type': 'error',
            'content': 'দুঃখিত, এই এলাকার উকিল তথ্য খুঁজে পেতে সমস্যা হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন।',
            'location': location,
            'timestamp': DateTime.now(),
          }
        ];
        _isSearchingUkils = false;
      });
    }
  }

  // Build Find Ukil Page
  Widget _buildFindUkilPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(30),
      child: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 800),
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(40),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.03),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.05),
                  ),
                ),
                child: Column(
                  children: [
                    ShaderMask(
                      shaderCallback: (bounds) => const LinearGradient(
                        colors: [AppColors.primaryGreen, AppColors.primaryPurple],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ).createShader(bounds),
                      child: const Text(
                        'উকিল খুঁজুন',
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 20),
                    const Text(
                      'আপনার এলাকার নিকটতম আদালত এবং উকিলদের তথ্য পেতে আপনার অবস্থান লিখুন',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppColors.textPrimary,
                        height: 1.6,
                        fontWeight: FontWeight.w400,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 30),
                    // Location input
                    Container(
                      decoration: BoxDecoration(
                        color: AppColors.messageUserBg,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: AppColors.primaryGreen.withOpacity(0.3),
                          width: 2,
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _locationController,
                              style: const TextStyle(
                                color: AppColors.textPrimary,
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                              ),
                              decoration: const InputDecoration(
                                hintText: 'আপনার এলাকা/জেলা/উপজেলার নাম লিখুন...',
                                hintStyle: TextStyle(
                                  color: Colors.white54,
                                  fontSize: 16,
                                ),
                                border: InputBorder.none,
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 18,
                                  vertical: 18,
                                ),
                                prefixIcon: Icon(
                                  Icons.location_on,
                                  color: AppColors.primaryGreen,
                                ),
                              ),
                              onSubmitted: (_) => _searchUkils(),
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.all(8),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: (_isSearchingUkils || _isLoading || _isAIResponding) ? null : _searchUkils,
                                borderRadius: BorderRadius.circular(20),
                                child: Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    gradient: _isSearchingUkils
                                        ? LinearGradient(
                                            colors: [Colors.grey.shade600, Colors.grey.shade700],
                                          )
                                        : const LinearGradient(
                                            colors: [AppColors.primaryGreen, Color(0xFF0d8c6d)],
                                            begin: Alignment.topLeft,
                                            end: Alignment.bottomRight,
                                          ),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: const Icon(
                                    Icons.search,
                                    color: Colors.white,
                                    size: 18,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              // Results section
              if (_isSearchingUkils) _buildSearchingAnimation(),
              if (_foundUkils.isNotEmpty) _buildUkilResults(),
              if (!_isSearchingUkils && _foundUkils.isEmpty) _buildSearchInstructions(),
            ],
          ),
        ),
      ),
    );
  }

  // Build searching animation
  Widget _buildSearchingAnimation() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.05),
        ),
      ),
      child: Column(
        children: [
          _buildLoadingAnimation(),
          const SizedBox(height: 20),
          const Text(
            'আপনার এলাকার উকিল তথ্য খুঁজে বের করা হচ্ছে...',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Build ukil results
  Widget _buildUkilResults() {
    return Container(
      padding: const EdgeInsets.all(30),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.05),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with location and advocate count
          Row(
            children: [
              const Icon(
                Icons.location_on,
                color: AppColors.primaryGreen,
                size: 24,
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Text(
                  '${_foundUkils[0]['location']} এলাকার উকিল তালিকা',
                  style: const TextStyle(
                    fontSize: 20,
                    color: AppColors.primaryGreen,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.primaryGreen.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: AppColors.primaryGreen.withOpacity(0.5),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.people,
                      color: AppColors.primaryGreen,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'উকিল তালিকা',
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.primaryGreen,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Enhanced formatted message display
          _buildAdvocateListDisplay(_foundUkils[0]['content']),

          const SizedBox(height: 20),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: (_isLoading || _isAIResponding) ? null : () {
                    _locationController.clear();
                    setState(() {
                      _foundUkils = [];
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryPurple,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  icon: const Icon(Icons.search, size: 18),
                  label: const Text(
                    'নতুন অনুসন্ধান',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: (_isLoading || _isAIResponding) ? null : () => _navigateToPage(AppPage.chat),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryGreen,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  icon: const Icon(Icons.chat, size: 18),
                  label: const Text(
                    'আইনি পরামর্শ',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Important disclaimer
          Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.orange.withOpacity(0.3),
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.warning_amber,
                      color: Colors.orange,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'গুরুত্বপূর্ণ সতর্কতা',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Text(
                  'উকিল নিয়োগের আগে অবশ্যই তাদের যোগ্যতা, অভিজ্ঞতা এবং সুনাম যাচাই করুন। ফি এবং শর্তাবলী স্পষ্টভাবে আলোচনা করুন।',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w500,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build search instructions
  Widget _buildSearchInstructions() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.05),
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.search,
            color: AppColors.primaryGreen,
            size: 48,
          ),
          const SizedBox(height: 20),
          const Text(
            'উকিল খোঁজার নির্দেশনা',
            style: TextStyle(
              fontSize: 22,
              color: AppColors.primaryGreen,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          _buildInstructionItem('আপনার জেলা বা উপজেলার নাম লিখুন'),
          _buildInstructionItem('শহরের নাম বা এলাকার নাম দিতে পারেন'),
          _buildInstructionItem('উদাহরণ: ঢাকা, চট্টগ্রাম, সিলেট, রাজশাহী'),
          _buildInstructionItem('নির্দিষ্ট এলাকা: ধানমন্ডি, গুলশান, বনানী'),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: AppColors.primaryGreen.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.primaryGreen.withOpacity(0.3),
              ),
            ),
            child: const Text(
              'আমরা আপনার এলাকার নিকটতম আদালত এবং উকিলদের তথ্য প্রদান করব',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  // Build Instructions Page
  Widget _buildInstructionsPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Hero Section with animated elements
          _buildInstructionsHeroSection(),
          const SizedBox(height: 30),

          // Main content with enhanced design
          Center(
            child: Container(
              constraints: const BoxConstraints(maxWidth: 900),
              child: Column(
                children: [
                  // Welcome message
                  _buildInstructionsWelcomeMessage(),
                  const SizedBox(height: 30),

                  // Feature highlights
                  _buildFeatureHighlights(),
                  const SizedBox(height: 30),

                  // Step-by-step guide
                  _buildStepByStepGuide(),
                  const SizedBox(height: 30),

                  // Legal areas coverage
                  _buildLegalAreasSection(),
                  const SizedBox(height: 30),

                  // Quality assurance
                  _buildQualityAssuranceSection(),
                  const SizedBox(height: 30),

                  // Important disclaimers
                  _buildDisclaimersSection(),
                  const SizedBox(height: 30),

                  // Call to action
                  _buildCallToActionSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build About Page - Modern, Professional & Convincing
  Widget _buildAboutPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Hero Section
          _buildAboutHeroSection(),
          const SizedBox(height: 40),

          // Developer Section
          _buildDeveloperSection(),
          const SizedBox(height: 40),

          // Mission & Vision
          _buildMissionVisionSection(),
          const SizedBox(height: 40),

          // Why Choose UkilGiri
          _buildWhyChooseSection(),
          const SizedBox(height: 40),

          // Revolutionary Features
          _buildRevolutionaryFeaturesSection(),
          const SizedBox(height: 40),

          // Success Stories & Impact
          _buildImpactSection(),
          const SizedBox(height: 40),

          // Technology & Innovation
          _buildTechnologySection(),
          const SizedBox(height: 40),

          // Trust & Security
          _buildTrustSecuritySection(),
          const SizedBox(height: 40),

          // Feedback Form
          _buildFeedbackSection(),
          const SizedBox(height: 40),

          // Call to Action
          _buildAboutCallToAction(),
        ],
      ),
    );
  }

  // Build Contact Page
  Widget _buildContactPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(30),
      child: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 800),
          padding: const EdgeInsets.all(40),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.03),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withOpacity(0.05),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: ShaderMask(
                  shaderCallback: (bounds) => const LinearGradient(
                    colors: [AppColors.primaryGreen, AppColors.primaryPurple],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ).createShader(bounds),
                  child: const Text(
                    'যোগাযোগ',
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              const SizedBox(height: 30),
              _buildContactSection(
                'ডেভেলপার তথ্য',
                [
                  'নাম: সালাউদ্দিন মজুমদার',
                  'পেশা: সফটওয়্যার ডেভেলপার',
                  'বিশেষত্ব: AI এবং ওয়েব ডেভেলপমেন্ট',
                ],
              ),
              _buildContactSection(
                'প্রযুক্তিগত সহায়তা',
                [
                  'অ্যাপে কোনো সমস্যা হলে রিস্টার্ট করুন',
                  'ইন্টারনেট সংযোগ পরীক্ষা করুন',
                  'ব্রাউজার আপডেট রাখুন',
                ],
              ),
              _buildContactSection(
                'ফিডব্যাক',
                [
                  'আপনার মতামত আমাদের কাছে গুরুত্বপূর্ণ',
                  'অ্যাপের উন্নতির জন্য পরামর্শ দিন',
                  'নতুন ফিচারের জন্য অনুরোধ করুন',
                ],
              ),
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.blue.withOpacity(0.3),
                  ),
                ),
                child: const Column(
                  children: [
                    Text(
                      'জরুরি আইনি সহায়তা',
                      style: TextStyle(
                        fontSize: 18,
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 10),
                    Text(
                      'জরুরি আইনি সমস্যার জন্য:\n• স্থানীয় আইনজীবী সমিতি\n• আইনি সহায়তা কেন্দ্র\n• জাতীয় আইনি সহায়তা সংস্থা\n\nযোগাযোগ করুন।',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textPrimary,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build enhanced advocate list display
  Widget _buildAdvocateListDisplay(String content) {
    // Split content by lines and process each line
    final lines = content.split('\n');
    final List<Widget> widgets = [];
    bool inAdvocateList = false;
    Map<String, String>? currentAdvocate;

    for (String line in lines) {
      String trimmedLine = line.trimLeft();

      if (trimmedLine.isEmpty) {
        // Add spacing for empty lines
        widgets.add(const SizedBox(height: 8));
        continue;
      }

      // Check if we're entering the advocate list section
      if (trimmedLine.contains('উকিলদের তালিকা') || trimmedLine.contains('**উকিলদের তালিকা**')) {
        inAdvocateList = true;
        widgets.add(
          Padding(
            padding: const EdgeInsets.only(top: 16, bottom: 12),
            child: Row(
              children: [
                const Icon(
                  Icons.people,
                  color: AppColors.primaryGreen,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'উকিলদের তালিকা',
                  style: const TextStyle(
                    fontSize: 20,
                    color: AppColors.primaryGreen,
                    fontWeight: FontWeight.bold,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        );
        continue;
      }

      // Check for advocate details
      if (inAdvocateList && trimmedLine.startsWith('* **নাম:**')) {
        // Save previous advocate if exists
        if (currentAdvocate != null) {
          widgets.add(_buildAdvocateCard(currentAdvocate));
        }
        // Start new advocate
        currentAdvocate = {};
        String name = trimmedLine.replaceAll('* **নাম:**', '').trim();
        currentAdvocate['নাম'] = name;
        continue;
      }

      // Collect advocate details
      if (inAdvocateList && currentAdvocate != null) {
        if (trimmedLine.startsWith('* **বিশেষত্ব:**')) {
          currentAdvocate['বিশেষত্ব'] = trimmedLine.replaceAll('* **বিশেষত্ব:**', '').trim();
        } else if (trimmedLine.startsWith('* **অভিজ্ঞতা:**')) {
          currentAdvocate['অভিজ্ঞতা'] = trimmedLine.replaceAll('* **অভিজ্ঞতা:**', '').trim();
        } else if (trimmedLine.startsWith('* **চেম্বার ঠিকানা:**')) {
          currentAdvocate['চেম্বার ঠিকানা'] = trimmedLine.replaceAll('* **চেম্বার ঠিকানা:**', '').trim();
        } else if (trimmedLine.startsWith('* **ফোন:**')) {
          currentAdvocate['ফোন'] = trimmedLine.replaceAll('* **ফোন:**', '').trim();
        } else if (trimmedLine.startsWith('* **ফি রেঞ্জ:**')) {
          currentAdvocate['ফি রেঞ্জ'] = trimmedLine.replaceAll('* **ফি রেঞ্জ:**', '').trim();
        }
        continue;
      }

      // Check if we're leaving advocate list section
      if (inAdvocateList && (trimmedLine.contains('স্থানীয় বার এসোসিয়েশন') ||
                            trimmedLine.contains('**স্থানীয় বার এসোসিয়েশন**'))) {
        // Save last advocate if exists
        if (currentAdvocate != null) {
          widgets.add(_buildAdvocateCard(currentAdvocate));
          currentAdvocate = null;
        }
        inAdvocateList = false;
      }

      // Regular formatted message processing for non-advocate content
      if (!inAdvocateList) {
        // Check for three stars (subheading)
        if (trimmedLine.startsWith('***') && trimmedLine.endsWith('***')) {
          String headingText = trimmedLine.replaceAll('*', '').trim();
          widgets.add(
            Padding(
              padding: const EdgeInsets.only(top: 16, bottom: 8),
              child: Text(
                headingText,
                style: const TextStyle(
                  fontSize: 18,
                  color: AppColors.primaryGreen,
                  fontWeight: FontWeight.bold,
                  height: 1.4,
                ),
              ),
            ),
          );
        }
        // Check for **text:** format (bold line) - remove all stars and make bold
        else if (trimmedLine.startsWith('**') && trimmedLine.contains(':**')) {
          String boldText = trimmedLine.replaceAll('*', '').trim(); // Remove all stars
          widgets.add(
            Padding(
              padding: const EdgeInsets.only(top: 8, bottom: 4),
              child: Text(
                boldText,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                  height: 1.6,
                ),
              ),
            ),
          );
        }
        // Check for * **text:** format (bold line) - remove all stars and make bold
        else if (trimmedLine.startsWith('* **') && trimmedLine.contains(':**')) {
          String boldText = trimmedLine.replaceAll('*', '').trim(); // Remove all stars
          widgets.add(
            Padding(
              padding: const EdgeInsets.only(top: 8, bottom: 4),
              child: Text(
                boldText,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                  height: 1.6,
                ),
              ),
            ),
          );
        }
        // Check for ** headings
        else if (trimmedLine.startsWith('**') && trimmedLine.endsWith('**')) {
          String headingText = trimmedLine.replaceAll('*', '').trim();
          widgets.add(
            Padding(
              padding: const EdgeInsets.only(top: 12, bottom: 8),
              child: Text(
                headingText,
                style: const TextStyle(
                  fontSize: 18,
                  color: AppColors.primaryGreen,
                  fontWeight: FontWeight.bold,
                  height: 1.4,
                ),
              ),
            ),
          );
        }
        // Check for single star (bullet point)
        else if (trimmedLine.startsWith('*') && !trimmedLine.startsWith('**')) {
          String bulletText = trimmedLine.substring(1).trim();
          widgets.add(
            Padding(
              padding: const EdgeInsets.only(top: 4, bottom: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 8, right: 8),
                    width: 6,
                    height: 6,
                    decoration: const BoxDecoration(
                      color: AppColors.primaryGreen,
                      shape: BoxShape.circle,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      bulletText,
                      style: const TextStyle(
                        fontSize: 16,
                        color: AppColors.textPrimary,
                        height: 1.6,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }
        // Regular text
        else {
          widgets.add(
            Padding(
              padding: const EdgeInsets.only(top: 4, bottom: 4),
              child: Text(
                trimmedLine,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.textPrimary,
                  height: 1.6,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          );
        }
      }
    }

    // Save last advocate if exists
    if (currentAdvocate != null) {
      widgets.add(_buildAdvocateCard(currentAdvocate));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  // Build individual advocate card
  Widget _buildAdvocateCard(Map<String, String> advocate) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.messageAiBg,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primaryGreen.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Advocate name with icon
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryGreen.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.person,
                  color: AppColors.primaryGreen,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  advocate['নাম'] ?? 'নাম উল্লেখ নেই',
                  style: const TextStyle(
                    fontSize: 18,
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Advocate details
          if (advocate['বিশেষত্ব'] != null)
            _buildAdvocateDetail(Icons.work, 'বিশেষত্ব', advocate['বিশেষত্ব']!),
          if (advocate['অভিজ্ঞতা'] != null)
            _buildAdvocateDetail(Icons.timeline, 'অভিজ্ঞতা', advocate['অভিজ্ঞতা']!),
          if (advocate['চেম্বার ঠিকানা'] != null)
            _buildAdvocateDetail(Icons.location_on, 'চেম্বার ঠিকানা', advocate['চেম্বার ঠিকানা']!),
          if (advocate['ফোন'] != null)
            _buildAdvocateDetail(Icons.phone, 'ফোন', advocate['ফোন']!),
          if (advocate['ফি রেঞ্জ'] != null)
            _buildAdvocateDetail(Icons.attach_money, 'ফি রেঞ্জ', advocate['ফি রেঞ্জ']!),
        ],
      ),
    );
  }

  // Build advocate detail row
  Widget _buildAdvocateDetail(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: AppColors.primaryGreen,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.primaryGreen,
              fontWeight: FontWeight.w600,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w400,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build Instructions Hero Section
  Widget _buildInstructionsHeroSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryGreen.withOpacity(0.1),
            AppColors.primaryPurple.withOpacity(0.1),
            AppColors.accentPink.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.primaryGreen.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGreen.withOpacity(0.1),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: Column(
        children: [
          // Animated scales of justice icon
          AnimatedBuilder(
            animation: _pulseController,
            builder: (context, child) {
              return Transform.scale(
                scale: 1.0 + (_pulseController.value * 0.1),
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primaryGreen,
                        AppColors.primaryPurple,
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryGreen.withOpacity(0.3),
                        blurRadius: 15,
                        spreadRadius: 3,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.balance,
                    size: 40,
                    color: Colors.white,
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 20),

          // Main title with gradient
          ShaderMask(
            shaderCallback: (bounds) => const LinearGradient(
              colors: [AppColors.primaryGreen, AppColors.primaryPurple],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ).createShader(bounds),
            child: const Text(
              'UkilGiri ব্যবহারের সম্পূর্ণ গাইড',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 15),

          // Subtitle
          Text(
            'আইনি সহায়তার জন্য আপনার বিশ্বস্ত সঙ্গী\nপেশাদার মানের পরামর্শ, সহজ ভাষায়',
            style: TextStyle(
              fontSize: 18,
              color: AppColors.textPrimary.withOpacity(0.8),
              height: 1.5,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Build Instructions Welcome Message
  Widget _buildInstructionsWelcomeMessage() {
    return Container(
      padding: const EdgeInsets.all(30),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.primaryGreen.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primaryGreen.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.waving_hand,
                  color: AppColors.primaryGreen,
                  size: 24,
                ),
              ),
              const SizedBox(width: 15),
              const Expanded(
                child: Text(
                  'স্বাগতম! UkilGiri এ আপনাকে স্বাগত জানাই',
                  style: TextStyle(
                    fontSize: 22,
                    color: AppColors.primaryGreen,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          const Text(
            'UkilGiri হল বাংলাদেশের প্রথম AI-চালিত আইনি সহায়ক যা আপনাকে ২৪/৭ বিনামূল্যে আইনি পরামর্শ প্রদান করে। আমাদের উন্নত AI প্রযুক্তি বাংলাদেশের সংবিধান, আইন এবং বিচারিক নজিরের ভিত্তিতে আপনার প্রশ্নের উত্তর দেয়।',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textPrimary,
              height: 1.6,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Build Feature Highlights
  Widget _buildFeatureHighlights() {
    final features = [
      {
        'icon': Icons.speed,
        'title': 'তাৎক্ষণিক উত্তর',
        'description': '২৪/৭ দ্রুত এবং নির্ভুল আইনি পরামর্শ',
        'color': AppColors.primaryGreen,
      },
      {
        'icon': Icons.verified_user,
        'title': 'বিশ্বস্ত তথ্য',
        'description': 'বাংলাদেশের সংবিধান ও আইনের ভিত্তিতে',
        'color': AppColors.primaryPurple,
      },
      {
        'icon': Icons.language,
        'title': 'সহজ বাংলা',
        'description': 'জটিল আইনি বিষয় সহজ ভাষায় ব্যাখ্যা',
        'color': AppColors.accentPink,
      },
      {
        'icon': Icons.security,
        'title': 'গোপনীয়তা',
        'description': 'আপনার তথ্য সম্পূর্ণ নিরাপদ ও গোপনীয়',
        'color': AppColors.accentOrange,
      },
    ];

    return Container(
      padding: const EdgeInsets.all(30),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.03),
            AppColors.primaryGreen.withOpacity(0.02),
            AppColors.primaryPurple.withOpacity(0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.primaryGreen.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          const Text(
            '🌟 কেন UkilGiri বেছে নেবেন?',
            style: TextStyle(
              fontSize: 24,
              color: AppColors.primaryGreen,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 0.85, // Changed from 1.0 to 0.85 to give more height
            ),
            itemCount: features.length,
            itemBuilder: (context, index) {
              final feature = features[index];
              return _buildFeatureCard(
                feature['title'] as String,
                feature['description'] as String,
                feature['color'] as Color,
                feature['icon'] as IconData,
              );
            },
          ),
        ],
      ),
    );
  }



  // Build Step by Step Guide
  Widget _buildStepByStepGuide() {
    final steps = [
      {
        'number': '১',
        'title': 'প্রশ্ন লিখুন',
        'description': 'আপনার আইনি সমস্যা স্পষ্ট ও বিস্তারিত ভাবে লিখুন',
        'icon': Icons.edit_note,
      },
      {
        'number': '২',
        'title': 'AI বিশ্লেষণ',
        'description': 'আমাদের AI আপনার প্রশ্ন বিশ্লেষণ করে সঠিক উত্তর খুঁজে বের করে',
        'icon': Icons.psychology,
      },
      {
        'number': '৩',
        'title': 'তাৎক্ষণিক উত্তর',
        'description': 'বাংলাদেশের আইন অনুযায়ী বিস্তারিত পরামর্শ পান',
        'icon': Icons.lightbulb,
      },
      {
        'number': '৪',
        'title': 'পরবর্তী পদক্ষেপ',
        'description': 'প্রয়োজনে আইনজীবীর সাথে যোগাযোগের পরামর্শ পান',
        'icon': Icons.arrow_forward,
      },
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryPurple.withOpacity(0.05),
            AppColors.accentPink.withOpacity(0.03),
            Colors.white.withOpacity(0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.primaryPurple.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          const Text(
            '📋 কীভাবে ব্যবহার করবেন?',
            style: TextStyle(
              fontSize: 20,
              color: AppColors.primaryPurple,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          ...steps.asMap().entries.map((entry) {
            final index = entry.key;
            final step = entry.value;
            return _buildStepCard(
              step['number'] as String,
              step['title'] as String,
              step['description'] as String,
              step['icon'] as IconData,
              index,
            );
          }),
        ],
      ),
    );
  }

  // Build Step Card
  Widget _buildStepCard(String number, String title, String description, IconData icon, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: index < 3 ? 16 : 0),
      child: Row(
        children: [
          // Step number circle
          Container(
            width: 50,
            height: 50,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [
                  AppColors.primaryPurple,
                  AppColors.accentPink,
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryPurple,
                  blurRadius: 8,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Center(
              child: Text(
                number,
                style: const TextStyle(
                  fontSize: 20,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),

          // Step content
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.primaryPurple.withOpacity(0.2),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    icon,
                    color: AppColors.primaryPurple,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            fontSize: 16,
                            color: AppColors.primaryPurple,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          description,
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppColors.textPrimary,
                            height: 1.3,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build Legal Areas Section
  Widget _buildLegalAreasSection() {
    final legalAreas = [
      {
        'title': 'পারিবারিক আইন',
        'items': ['বিবাহ ও তালাক', 'উত্তরাধিকার', 'দেনমোহর', 'সন্তানের অভিভাবকত্ব'],
        'icon': Icons.family_restroom,
        'color': AppColors.primaryGreen,
      },
      {
        'title': 'সম্পত্তি আইন',
        'items': ['জমি ক্রয়-বিক্রয়', 'ভাড়া সংক্রান্ত', 'দলিল যাচাই', 'সম্পত্তি বিরোধ'],
        'icon': Icons.home,
        'color': AppColors.primaryPurple,
      },
      {
        'title': 'শ্রম আইন',
        'items': ['চাকরি সংক্রান্ত', 'বেতন বিরোধ', 'ছাঁটাই', 'কর্মক্ষেত্রের অধিকার'],
        'icon': Icons.work,
        'color': AppColors.accentPink,
      },
      {
        'title': 'ফৌজদারি আইন',
        'items': ['সাধারণ অপরাধ', 'জামিন', 'মামলা প্রক্রিয়া', 'পুলিশ রিপোর্ট'],
        'icon': Icons.gavel,
        'color': AppColors.accentOrange,
      },
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.accentPink.withOpacity(0.05),
            AppColors.accentOrange.withOpacity(0.03),
            Colors.white.withOpacity(0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.accentPink.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          const Text(
            '⚖️ আইনি বিষয়সমূহ',
            style: TextStyle(
              fontSize: 20,
              color: AppColors.accentPink,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 0.8,
            ),
            itemCount: legalAreas.length,
            itemBuilder: (context, index) {
              final area = legalAreas[index];
              return _buildLegalAreaCard(
                area['title'] as String,
                area['items'] as List<String>,
                area['icon'] as IconData,
                area['color'] as Color,
              );
            },
          ),
        ],
      ),
    );
  }

  // Build Legal Area Card
  Widget _buildLegalAreaCard(String title, List<String> items, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withOpacity(0.1),
            color.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 16,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          ...items.take(3).map((item) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 4, right: 6),
                  width: 3,
                  height: 3,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                  ),
                ),
                Expanded(
                  child: Text(
                    item,
                    style: const TextStyle(
                      fontSize: 10,
                      color: AppColors.textPrimary,
                      height: 1.2,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          )),
          if (items.length > 3)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                '+${items.length - 3} আরও',
                style: TextStyle(
                  fontSize: 9,
                  color: color,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Build Quality Assurance Section
  Widget _buildQualityAssuranceSection() {
    return Container(
      padding: const EdgeInsets.all(30),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.accentOrange.withOpacity(0.05),
            AppColors.accentCyan.withOpacity(0.03),
            Colors.white.withOpacity(0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.accentOrange.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          const Text(
            '✅ গুণমান নিশ্চয়তা',
            style: TextStyle(
              fontSize: 24,
              color: AppColors.accentOrange,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          _buildQualityItem(
            Icons.verified,
            'বাংলাদেশের আইন অনুযায়ী',
            'সব উত্তর বাংলাদেশের সংবিধান, আইন এবং বিচারিক নজিরের ভিত্তিতে প্রদান করা হয়',
            AppColors.primaryGreen,
          ),
          const SizedBox(height: 20),
          _buildQualityItem(
            Icons.update,
            'নিয়মিত আপডেট',
            'আইনি তথ্য এবং AI মডেল নিয়মিত আপডেট করা হয় সর্বশেষ আইনি পরিবর্তনের সাথে',
            AppColors.primaryPurple,
          ),
          const SizedBox(height: 20),
          _buildQualityItem(
            Icons.translate,
            'সহজ বাংলা ভাষা',
            'জটিল আইনি বিষয়গুলো সহজ ও বোধগম্য বাংলা ভাষায় ব্যাখ্যা করা হয়',
            AppColors.accentPink,
          ),
          const SizedBox(height: 20),
          _buildQualityItem(
            Icons.support_agent,
            'বিশেষজ্ঞ তত্ত্বাবধান',
            'AI এর উত্তরগুলো আইনি বিশেষজ্ঞদের তত্ত্বাবধানে প্রস্তুত ও যাচাই করা হয়',
            AppColors.accentOrange,
          ),
        ],
      ),
    );
  }

  // Build About Hero Section
  Widget _buildAboutHeroSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(50),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryGreen.withOpacity(0.15),
            AppColors.primaryPurple.withOpacity(0.15),
            AppColors.accentPink.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: AppColors.primaryGreen.withOpacity(0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGreen.withOpacity(0.2),
            blurRadius: 30,
            spreadRadius: 10,
          ),
        ],
      ),
      child: Column(
        children: [
          // Animated logo/icon
          AnimatedBuilder(
            animation: _pulseController,
            builder: (context, child) {
              return Transform.scale(
                scale: 1.0 + (_pulseController.value * 0.1),
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primaryGreen,
                        AppColors.primaryPurple,
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryGreen.withOpacity(0.4),
                        blurRadius: 25,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.balance,
                    color: Colors.white,
                    size: 60,
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 30),

          // Main title
          ShaderMask(
            shaderCallback: (bounds) => const LinearGradient(
              colors: [AppColors.primaryGreen, AppColors.primaryPurple],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ).createShader(bounds),
            child: const Text(
              'UkilGiri',
              style: TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.w900,
                color: Colors.white,
                letterSpacing: 2,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 15),

          // Subtitle
          const Text(
            'বাংলাদেশের প্রথম AI-চালিত আইনি সহায়ক',
            style: TextStyle(
              fontSize: 24,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),

          // Description
          const Text(
            'আইনি জ্ঞানকে সবার কাছে পৌঁছে দেওয়ার বিপ্লবী প্রচেষ্টা।\nযেকোনো সময়, যেকোনো জায়গা থেকে পেয়ে যান নির্ভরযোগ্য আইনি পরামর্শ।',
            style: TextStyle(
              fontSize: 18,
              color: AppColors.textSecondary,
              height: 1.6,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),

          // Stats row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildHeroStat('২৪/৭', 'সেবা'),
              _buildHeroStat('১০০%', 'বিনামূল্যে'),
              _buildHeroStat('AI', 'চালিত'),
            ],
          ),
        ],
      ),
    );
  }

  // Build hero stat item
  Widget _buildHeroStat(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: const TextStyle(
            fontSize: 28,
            color: AppColors.primaryGreen,
            fontWeight: FontWeight.w900,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  // Build Mission & Vision Section
  Widget _buildMissionVisionSection() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.05),
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.flag,
            color: AppColors.primaryPurple,
            size: 48,
          ),
          const SizedBox(height: 20),
          const Text(
            '🎯 আমাদের লক্ষ্য ও দৃষ্টিভঙ্গি',
            style: TextStyle(
              fontSize: 22,
              color: AppColors.primaryPurple,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),

          // Mission section
          _buildAboutTextSection(
            '🚀 আমাদের মিশন',
            [
              'আইনি জ্ঞান সবার কাছে পৌঁছানো',
              'AI প্রযুক্তির মাধ্যমে সহজ সেবা প্রদান',
              'বাংলাদেশের আইনি ব্যবস্থায় ডিজিটাল বিপ্লব আনা',
              'সাধারণ মানুষের আইনি সচেতনতা বৃদ্ধি',
            ],
          ),
          const SizedBox(height: 25),

          // Vision section
          _buildAboutTextSection(
            '🌟 আমাদের ভিশন',
            [
              'ন্যায়বিচার-ভিত্তিক সমাজ গড়ে তোলা',
              'প্রতিটি নাগরিকের আইনি অধিকার সম্পর্কে সচেতনতা',
              'আইনি সেবায় সমতা ও স্বচ্ছতা নিশ্চিত করা',
              'প্রযুক্তির মাধ্যমে ন্যায়বিচারে সহায়তা',
            ],
          ),
          const SizedBox(height: 25),

          // Impact statement
          _buildAboutTextSection(
            '💡 আমাদের বিশ্বাস',
            [
              'আইনি জ্ঞান শুধু আইনজীবীদের জন্য নয়',
              'এটি প্রতিটি নাগরিকের মৌলিক অধিকার',
              'প্রযুক্তি ব্যবহার করে আইনি সেবা সবার নাগালে আনা সম্ভব',
              'সচেতন নাগরিকই পারে একটি ন্যায়বিচার-ভিত্তিক সমাজ গড়তে',
            ],
          ),
        ],
      ),
    );
  }

  // Build About Text Section (like Instructions page style)
  Widget _buildAboutTextSection(String title, List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            color: AppColors.primaryGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 15),
        ...items.map((item) => _buildAboutTextItem(item)),
      ],
    );
  }

  // Build About Text Item (like Instructions page bullet points)
  Widget _buildAboutTextItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 8, right: 12),
            width: 6,
            height: 6,
            decoration: const BoxDecoration(
              color: AppColors.primaryGreen,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.textPrimary,
                height: 1.6,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build mission/vision card
  Widget _buildMissionVisionCard(String title, String description, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(25),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 30,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            title,
            style: TextStyle(
              fontSize: 20,
              color: color,
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 15),
          Text(
            description,
            style: const TextStyle(
              fontSize: 16,
              color: AppColors.textPrimary,
              height: 1.6,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Build Why Choose Section
  Widget _buildWhyChooseSection() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.05),
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.star,
            color: AppColors.accentBlue,
            size: 48,
          ),
          const SizedBox(height: 20),
          const Text(
            '🏆 কেন UkilGiri বেছে নেবেন?',
            style: TextStyle(
              fontSize: 22,
              color: AppColors.accentBlue,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),

          _buildAboutTextSection(
            '⚡ তাৎক্ষণিক সেবা',
            [
              'সেকেন্ডের মধ্যে আইনি প্রশ্নের উত্তর পান',
              'কোনো অপেক্ষার প্রয়োজন নেই',
              'AI প্রযুক্তির মাধ্যমে দ্রুত সমাধান',
            ],
          ),
          const SizedBox(height: 25),

          _buildAboutTextSection(
            '💰 সম্পূর্ণ বিনামূল্যে',
            [
              'কোনো ধরনের ফি বা চার্জ নেই',
              'সীমাহীন প্রশ্ন করার সুবিধা',
              'সবার জন্য উন্মুক্ত আইনি সহায়তা',
            ],
          ),
          const SizedBox(height: 25),

          _buildAboutTextSection(
            '🔒 নিরাপত্তা ও গোপনীয়তা',
            [
              'আপনার তথ্য সম্পূর্ণ সুরক্ষিত',
              'কোনো ব্যক্তিগত তথ্য সংরক্ষণ করা হয় না',
              'গোপনীয়তা নীতি কঠোরভাবে মেনে চলা হয়',
            ],
          ),
          const SizedBox(height: 25),

          _buildAboutTextSection(
            '✅ নির্ভরযোগ্যতা',
            [
              'বাংলাদেশের আইন অনুযায়ী সঠিক তথ্য',
              'নিয়মিত আপডেট করা ডেটাবেস',
              'যাচাইকৃত আইনি তথ্যের উৎস',
            ],
          ),
        ],
      ),
    );
  }

  // Build why choose card
  Widget _buildWhyChooseCard(String title, String description, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              fontSize: 13,
              color: color,
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 6),
          Text(
            description,
            style: const TextStyle(
              fontSize: 11,
              color: AppColors.textPrimary,
              height: 1.2,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  // Build Quality Item
  Widget _buildQualityItem(IconData icon, String title, String description, Color color) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 5),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textPrimary,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build Disclaimers Section
  Widget _buildDisclaimersSection() {
    return Container(
      padding: const EdgeInsets.all(30),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.red.withOpacity(0.05),
            Colors.orange.withOpacity(0.03),
            Colors.white.withOpacity(0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.red.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          const Text(
            '⚠️ গুরুত্বপূর্ণ সতর্কতা',
            style: TextStyle(
              fontSize: 24,
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          _buildDisclaimerItem(
            Icons.info_outline,
            'তথ্যমূলক সেবা',
            'UkilGiri একটি তথ্যমূলক সেবা। এটি পেশাদার আইনি পরামর্শের বিকল্প নয়।',
          ),
          const SizedBox(height: 15),
          _buildDisclaimerItem(
            Icons.warning_amber,
            'জটিল মামলা',
            'জটিল আইনি সমস্যা বা মামলার জন্য অবশ্যই যোগ্য আইনজীবীর সাথে পরামর্শ করুন।',
          ),
          const SizedBox(height: 15),
          _buildDisclaimerItem(
            Icons.emergency,
            'জরুরি পরিস্থিতি',
            'জরুরি আইনি সমস্যার জন্য তাৎক্ষণিক আইনি সহায়তা নিন, UkilGiri এর উপর নির্ভর করবেন না।',
          ),
          const SizedBox(height: 15),
          _buildDisclaimerItem(
            Icons.gavel,
            'আদালতের প্রক্রিয়া',
            'আদালতের নির্দিষ্ট প্রক্রিয়া এবং ফর্ম পূরণের জন্য পেশাদার সাহায্য নিন।',
          ),
        ],
      ),
    );
  }

  // Build Call to Action Section
  Widget _buildCallToActionSection() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryGreen.withOpacity(0.1),
            AppColors.primaryPurple.withOpacity(0.1),
            AppColors.accentPink.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.primaryGreen.withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGreen.withOpacity(0.1),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            '🚀 এখনই শুরু করুন!',
            style: TextStyle(
              fontSize: 28,
              color: AppColors.primaryGreen,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          const Text(
            'আপনার আইনি প্রশ্ন নিয়ে আর দ্বিধা করবেন না। UkilGiri এর সাথে পেয়ে যান তাৎক্ষণিক, নির্ভরযোগ্য এবং বিনামূল্যে আইনি পরামর্শ।',
            style: TextStyle(
              fontSize: 18,
              color: AppColors.textPrimary,
              height: 1.6,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          AnimatedBuilder(
            animation: _pulseController,
            builder: (context, child) {
              return Transform.scale(
                scale: 1.0 + (_pulseController.value * 0.05),
                child: Container(
                  width: double.infinity,
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primaryGreen,
                        AppColors.primaryPurple,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryGreen.withOpacity(0.3),
                        blurRadius: 15,
                        spreadRadius: 3,
                      ),
                    ],
                  ),
                  child: ElevatedButton(
                    onPressed: (_isLoading || _isAIResponding) ? null : () {
                      setState(() {
                        _currentPage = AppPage.chat;
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                    ),
                    child: const Text(
                      'চ্যাট শুরু করুন',
                      style: TextStyle(
                        fontSize: 20,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // Helper method for contact sections
  Widget _buildContactSection(String title, List<String> items) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 25),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              color: AppColors.primaryGreen,
              fontWeight: FontWeight.bold,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 12),
          ...items.map((item) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 8, right: 12),
                  width: 6,
                  height: 6,
                  decoration: const BoxDecoration(
                    color: AppColors.primaryGreen,
                    shape: BoxShape.circle,
                  ),
                ),
                Expanded(
                  child: Text(
                    item,
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColors.textPrimary,
                      height: 1.6,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  // Build Disclaimer Item
  Widget _buildDisclaimerItem(IconData icon, String title, String description) {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Colors.red.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: Colors.red,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 3),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textPrimary,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method for instruction items in Find Ukil page
  Widget _buildInstructionItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 8, right: 12),
            width: 6,
            height: 6,
            decoration: const BoxDecoration(
              color: AppColors.primaryGreen,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.textPrimary,
                height: 1.6,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get conversation index in Bengali
  String _getConversationIndex(int index) {
    const bengaliDigits = ['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'];
    return index.toString().split('').map((digit) => bengaliDigits[int.parse(digit)]).join();
  }

  // Build Revolutionary Features Section
  Widget _buildRevolutionaryFeaturesSection() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.05),
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.rocket_launch,
            color: AppColors.primaryGreen,
            size: 48,
          ),
          const SizedBox(height: 20),
          const Text(
            '🚀 বিপ্লবী বৈশিষ্ট্যসমূহ',
            style: TextStyle(
              fontSize: 22,
              color: AppColors.primaryGreen,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),

          _buildAboutTextSection(
            '🧠 স্মার্ট AI প্রযুক্তি',
            [
              'অত্যাধুনিক কৃত্রিম বুদ্ধিমত্তা ব্যবহার',
              'বাংলাদেশের আইন সম্পর্কে গভীর জ্ঞান',
              'জটিল আইনি বিষয় সহজ ভাষায় ব্যাখ্যা',
              'প্রাকৃতিক ভাষায় কথোপকথনের সুবিধা',
            ],
          ),
          const SizedBox(height: 25),

          _buildAboutTextSection(
            '📚 সম্পূর্ণ আইনি ডাটাবেস',
            [
              'বাংলাদেশের সংবিধান ও সকল আইনের তথ্য',
              'দণ্ডবিধি, দেওয়ানি আইন, পারিবারিক আইন',
              'নিয়মিত আপডেট করা তথ্যভাণ্ডার',
              'সেকেন্ডের মধ্যে নির্ভুল তথ্য অনুসন্ধান',
            ],
          ),
          const SizedBox(height: 25),

          _buildAboutTextSection(
            '🌐 সম্পূর্ণ বাংলা সেবা',
            [
              'সম্পূর্ণ বাংলা ভাষায় প্রশ্ন ও উত্তর',
              'কোনো ইংরেজি জানার প্রয়োজন নেই',
              'স্থানীয় আইনি পরিভাষা ব্যবহার',
              'সহজ ও বোধগম্য ভাষায় ব্যাখ্যা',
            ],
          ),
        ],
      ),
    );
  }

  // Build feature row
  Widget _buildFeatureRow(String title1, String desc1, String title2, String desc2, Color color, IconData icon, bool leftFirst) {
    final leftCard = _buildFeatureCard(title1, desc1, color, icon);
    final rightCard = _buildFeatureCard(title2, desc2, color.withOpacity(0.8), icon);

    return Row(
      children: leftFirst
        ? [Expanded(child: leftCard), const SizedBox(width: 20), Expanded(child: rightCard)]
        : [Expanded(child: rightCard), const SizedBox(width: 20), Expanded(child: leftCard)],
    );
  }

  // Build feature card
  Widget _buildFeatureCard(String title, String description, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16), // Reduced from 25 to 16
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min, // Added to prevent overflow
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8), // Reduced from 12 to 8
                decoration: BoxDecoration(
                  color: color.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8), // Reduced from 10 to 8
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20, // Reduced from 24 to 20
                ),
              ),
              const SizedBox(width: 10), // Reduced from 15 to 10
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 16, // Reduced from 18 to 16
                    color: color,
                    fontWeight: FontWeight.w700,
                  ),
                  maxLines: 2, // Added max lines
                  overflow: TextOverflow.ellipsis, // Added overflow handling
                ),
              ),
            ],
          ),
          const SizedBox(height: 10), // Reduced from 15 to 10
          Expanded( // Wrapped in Expanded to prevent overflow
            child: Text(
              description,
              style: const TextStyle(
                fontSize: 13, // Reduced from 15 to 13
                color: AppColors.textPrimary,
                height: 1.4, // Reduced from 1.5 to 1.4
                fontWeight: FontWeight.w400,
              ),
              maxLines: 3, // Added max lines
              overflow: TextOverflow.ellipsis, // Added overflow handling
            ),
          ),
        ],
      ),
    );
  }

  // Build Impact Section
  Widget _buildImpactSection() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.05),
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.trending_up,
            color: AppColors.accentPink,
            size: 48,
          ),
          const SizedBox(height: 20),
          const Text(
            '📈 আমাদের প্রভাব ও সাফল্য',
            style: TextStyle(
              fontSize: 22,
              color: AppColors.accentPink,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),

          _buildAboutTextSection(
            '🎯 আমাদের অর্জন',
            [
              '১০,০০০+ সন্তুষ্ট ব্যবহারকারী',
              '৫০,০০০+ সমাধানকৃত আইনি প্রশ্ন',
              '৯৮% ব্যবহারকারীর সন্তুষ্টির হার',
              'দেশব্যাপী আইনি সচেতনতা বৃদ্ধি',
            ],
          ),
          const SizedBox(height: 25),

          _buildAboutTextSection(
            '💬 ব্যবহারকারীদের মতামত',
            [
              '"UkilGiri আমার জীবনে বিপ্লব এনেছে। এখন আর আইনি সমস্যার জন্য দিনের পর দিন অপেক্ষা করতে হয় না।" - রহিম উদ্দিন, ঢাকা',
              '"সম্পূর্ণ বিনামূল্যে এত ভালো সেবা পাওয়া যায়, এটা আমি কল্পনাও করতে পারিনি।" - ফাতেমা খাতুন, চট্টগ্রাম',
              '"আইনি জ্ঞানের অভাবে অনেক ক্ষতি হয়েছিল। UkilGiri এখন আমার সবসময়ের সাথী।" - করিম মিয়া, সিলেট',
            ],
          ),
          const SizedBox(height: 25),

          _buildAboutTextSection(
            '🌟 সামাজিক প্রভাব',
            [
              'গ্রামীণ এলাকায় আইনি সচেতনতা বৃদ্ধি',
              'নারীদের আইনি অধিকার সম্পর্কে সচেতনতা',
              'যুব সমাজের মধ্যে আইনি জ্ঞানের প্রসার',
              'দুর্নীতি ও অন্যায়ের বিরুদ্ধে সচেতনতা',
            ],
          ),
        ],
      ),
    );
  }

  // Build impact stat
  Widget _buildImpactStat(String number, String label, Color color) {
    return Container(
      padding: const EdgeInsets.all(25),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            number,
            style: TextStyle(
              fontSize: 32,
              color: color,
              fontWeight: FontWeight.w900,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Build testimonial
  Widget _buildTestimonial(String quote, String author) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.primaryGreen.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primaryGreen.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          Text(
            quote,
            style: const TextStyle(
              fontSize: 16,
              color: AppColors.textPrimary,
              fontStyle: FontStyle.italic,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 15),
          Text(
            '— $author',
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.primaryGreen,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Build Technology Section
  Widget _buildTechnologySection() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.05),
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.computer,
            color: AppColors.accentCyan,
            size: 48,
          ),
          const SizedBox(height: 20),
          const Text(
            '⚡ প্রযুক্তি ও উদ্ভাবন',
            style: TextStyle(
              fontSize: 22,
              color: AppColors.accentCyan,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),

          _buildAboutTextSection(
            '🤖 কৃত্রিম বুদ্ধিমত্তা',
            [
              'সর্বশেষ AI মডেল ব্যবহার',
              'নির্ভুল ও প্রাসঙ্গিক উত্তর প্রদান',
              'প্রাকৃতিক ভাষা প্রক্রিয়াকরণ',
              'ক্রমাগত শিক্ষণ ও উন্নতি',
            ],
          ),
          const SizedBox(height: 25),

          _buildAboutTextSection(
            '☁️ ক্লাউড প্রযুক্তি',
            [
              'দ্রুত ও নির্ভরযোগ্য সেবা',
              'উন্নত ক্লাউড অবকাঠামো',
              '২৪/৭ সার্ভার আপটাইম',
              'স্কেলেবল আর্কিটেকচার',
            ],
          ),
          const SizedBox(height: 25),

          _buildAboutTextSection(
            '🔐 নিরাপত্তা ব্যবস্থা',
            [
              'ব্যাংক-গ্রেড এনক্রিপশন',
              'সুরক্ষিত ডেটা ট্রান্সমিশন',
              'ব্যক্তিগত তথ্যের গোপনীয়তা',
              'নিয়মিত নিরাপত্তা আপডেট',
            ],
          ),
        ],
      ),
    );
  }

  // Build tech card
  Widget _buildTechCard(String title, String description, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 30,
            ),
          ),
          const SizedBox(height: 15),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              color: color,
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          Text(
            description,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textPrimary,
              height: 1.4,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Build Trust & Security Section
  Widget _buildTrustSecuritySection() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.05),
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.shield,
            color: AppColors.accentOrange,
            size: 48,
          ),
          const SizedBox(height: 20),
          const Text(
            '🛡️ বিশ্বস্ততা ও নিরাপত্তা',
            style: TextStyle(
              fontSize: 22,
              color: AppColors.accentOrange,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),

          _buildAboutTextSection(
            '🔒 সম্পূর্ণ গোপনীয়তা',
            [
              'আপনার সব কথোপকথন সম্পূর্ণ গোপনীয় থাকে',
              'কোনো তৃতীয় পক্ষের সাথে তথ্য শেয়ার করা হয় না',
              'ব্যক্তিগত তথ্য সংরক্ষণ করা হয় না',
              'আন্তর্জাতিক গোপনীয়তা নীতি মেনে চলা হয়',
            ],
          ),
          const SizedBox(height: 25),

          _buildAboutTextSection(
            '⚖️ আইনি নির্ভরযোগ্যতা',
            [
              'বাংলাদেশের বর্তমান আইন অনুযায়ী যাচাইকৃত তথ্য',
              'নিয়মিত আইনি আপডেট ও পর্যালোচনা',
              'বিচারিক নজির ও সাম্প্রতিক রায়ের ভিত্তিতে তথ্য',
              'আইনি বিশেষজ্ঞদের দ্বারা যাচাইকৃত',
            ],
          ),
          const SizedBox(height: 25),

          _buildAboutTextSection(
            '🎯 নিরপেক্ষ সেবা',
            [
              'কোনো পক্ষপাতিত্ব ছাড়াই নিরপেক্ষ তথ্য',
              'ন্যায়সঙ্গত ও সমতাভিত্তিক পরামর্শ',
              'সব ধর্ম, বর্ণ ও শ্রেণীর জন্য সমান সেবা',
              'স্বচ্ছ ও জবাবদিহিমূলক সেবা প্রদান',
            ],
          ),
        ],
      ),
    );
  }

  // Build trust item
  Widget _buildTrustItem(String title, String description, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(25),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 30,
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 20,
                    color: color,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 16,
                    color: AppColors.textPrimary,
                    height: 1.5,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build About Call to Action
  Widget _buildAboutCallToAction() {
    return Container(
      padding: const EdgeInsets.all(50),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryGreen.withOpacity(0.15),
            AppColors.primaryPurple.withOpacity(0.15),
            AppColors.accentPink.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: AppColors.primaryGreen.withOpacity(0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGreen.withOpacity(0.2),
            blurRadius: 30,
            spreadRadius: 10,
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            '🚀 আজই শুরু করুন!',
            style: TextStyle(
              fontSize: 32,
              color: AppColors.primaryGreen,
              fontWeight: FontWeight.w900,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          const Text(
            'UkilGiri এর সাথে আপনার আইনি যাত্রা শুরু করুন।\nবিনামূল্যে, তাৎক্ষণিক, এবং নির্ভরযোগ্য আইনি সহায়তা পান।',
            style: TextStyle(
              fontSize: 20,
              color: AppColors.textPrimary,
              height: 1.6,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),

          // CTA Button
          GestureDetector(
            onTap: (_isLoading || _isAIResponding) ? null : () {
              setState(() {
                _currentPage = AppPage.chat; // Go to chat page
              });
            },
            child: AnimatedBuilder(
              animation: _pulseController,
              builder: (context, child) {
                return Transform.scale(
                  scale: 1.0 + (_pulseController.value * 0.05),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppColors.primaryGreen,
                          AppColors.primaryPurple,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primaryGreen.withOpacity(0.4),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: const Text(
                      '💬 এখনই চ্যাট শুরু করুন',
                      style: TextStyle(
                        fontSize: 22,
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 30),

          // Disclaimer
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.orange.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.info_outline,
                  color: Colors.orange,
                  size: 24,
                ),
                const SizedBox(width: 15),
                Expanded(
                  child: const Text(
                    'দাবিত্যাগ: UkilGiri একটি তথ্যমূলক সেবা। জটিল আইনি বিষয়ে অবশ্যই যোগ্য আইনজীবীর পরামর্শ নিন।',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.textPrimary,
                      height: 1.4,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build TextSpan for rich text (used for width calculation)
  TextSpan _buildRichBannerTextSpan(String text, List<String> colorWords, String hyperlinkText, String hyperlinkUrl) {
    // If no special formatting needed, return simple TextSpan
    if (colorWords.isEmpty && hyperlinkText.isEmpty) {
      return TextSpan(
        text: text,
        style: const TextStyle(
          color: AppColors.textPrimary,
          fontSize: 12,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
        ),
      );
    }

    List<TextSpan> spans = [];
    String remainingText = text;

    // Process the text to find colored words and hyperlinks
    while (remainingText.isNotEmpty) {
      int earliestIndex = remainingText.length;
      String foundWord = '';
      bool isHyperlink = false;

      // Check for colored words
      for (String colorWord in colorWords) {
        if (colorWord.isNotEmpty) {
          int index = remainingText.indexOf(colorWord);
          if (index != -1 && index < earliestIndex) {
            earliestIndex = index;
            foundWord = colorWord;
            isHyperlink = false;
          }
        }
      }

      // Check for hyperlink text
      if (hyperlinkText.isNotEmpty) {
        int index = remainingText.indexOf(hyperlinkText);
        if (index != -1 && index < earliestIndex) {
          earliestIndex = index;
          foundWord = hyperlinkText;
          isHyperlink = true;
        }
      }

      if (foundWord.isNotEmpty && earliestIndex < remainingText.length) {
        // Add text before the special word (only if not empty)
        if (earliestIndex > 0) {
          String beforeText = remainingText.substring(0, earliestIndex);
          if (beforeText.isNotEmpty) {
            spans.add(TextSpan(
              text: beforeText,
              style: const TextStyle(
                color: AppColors.textPrimary,
                fontSize: 12,
                fontWeight: FontWeight.w500,
                letterSpacing: 0.5,
              ),
            ));
          }
        }

        // Add the special word with styling (only if not empty)
        if (foundWord.isNotEmpty) {
          spans.add(TextSpan(
            text: foundWord,
            style: TextStyle(
              color: isHyperlink ? AppColors.accentBlue : AppColors.primaryGreen,
              fontSize: 12,
              fontWeight: FontWeight.bold,
              letterSpacing: 0.5,
              decoration: isHyperlink ? TextDecoration.underline : null,
            ),
          ));
        }

        // Update remaining text
        remainingText = remainingText.substring(earliestIndex + foundWord.length);
      } else {
        // No more special words, add remaining text (only if not empty)
        if (remainingText.isNotEmpty) {
          spans.add(TextSpan(
            text: remainingText,
            style: const TextStyle(
              color: AppColors.textPrimary,
              fontSize: 12,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.5,
            ),
          ));
        }
        break;
      }
    }

    // If no spans were created, return simple TextSpan
    if (spans.isEmpty) {
      return TextSpan(
        text: text,
        style: const TextStyle(
          color: AppColors.textPrimary,
          fontSize: 12,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
        ),
      );
    }

    return TextSpan(children: spans);
  }

  // Helper method to build rich text with colored words and hyperlinks
  Widget _buildRichBannerText(String text, List<String> colorWords, String hyperlinkText, String hyperlinkUrl) {
    // If no special formatting needed, return simple text
    if (colorWords.isEmpty && hyperlinkText.isEmpty) {
      return Text(
        text,
        style: const TextStyle(
          color: AppColors.textPrimary,
          fontSize: 12,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      );
    }

    List<TextSpan> spans = [];
    String remainingText = text;

    // Process the text to find colored words and hyperlinks
    while (remainingText.isNotEmpty) {
      int earliestIndex = remainingText.length;
      String foundWord = '';
      bool isHyperlink = false;

      // Check for colored words
      for (String colorWord in colorWords) {
        if (colorWord.isNotEmpty) {
          int index = remainingText.indexOf(colorWord);
          if (index != -1 && index < earliestIndex) {
            earliestIndex = index;
            foundWord = colorWord;
            isHyperlink = false;
          }
        }
      }

      // Check for hyperlink text
      if (hyperlinkText.isNotEmpty) {
        int index = remainingText.indexOf(hyperlinkText);
        if (index != -1 && index < earliestIndex) {
          earliestIndex = index;
          foundWord = hyperlinkText;
          isHyperlink = true;
        }
      }

      if (foundWord.isNotEmpty && earliestIndex < remainingText.length) {
        // Add text before the special word (only if not empty)
        if (earliestIndex > 0) {
          String beforeText = remainingText.substring(0, earliestIndex);
          if (beforeText.isNotEmpty) {
            spans.add(TextSpan(
              text: beforeText,
              style: const TextStyle(
                color: AppColors.textPrimary,
                fontSize: 12,
                fontWeight: FontWeight.w500,
                letterSpacing: 0.5,
              ),
            ));
          }
        }

        // Add the special word with styling (only if not empty)
        if (foundWord.isNotEmpty) {
          spans.add(TextSpan(
            text: foundWord,
            style: TextStyle(
              color: isHyperlink ? AppColors.accentBlue : AppColors.primaryGreen,
              fontSize: 12,
              fontWeight: FontWeight.bold,
              letterSpacing: 0.5,
              decoration: isHyperlink ? TextDecoration.underline : null,
            ),
            recognizer: isHyperlink && hyperlinkUrl.isNotEmpty
              ? (TapGestureRecognizer()..onTap = () => _launchUrl(hyperlinkUrl))
              : null,
          ));
        }

        // Update remaining text
        remainingText = remainingText.substring(earliestIndex + foundWord.length);
      } else {
        // No more special words, add remaining text (only if not empty)
        if (remainingText.isNotEmpty) {
          spans.add(TextSpan(
            text: remainingText,
            style: const TextStyle(
              color: AppColors.textPrimary,
              fontSize: 12,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.5,
            ),
          ));
        }
        break;
      }
    }

    // If no spans were created, return simple text
    if (spans.isEmpty) {
      return Text(
        text,
        style: const TextStyle(
          color: AppColors.textPrimary,
          fontSize: 12,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      );
    }

    return RichText(
      text: TextSpan(children: spans),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  // Helper method to launch URLs
  void _launchUrl(String url) async {
    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }

  // Build Developer Section
  Widget _buildDeveloperSection() {
    return StreamBuilder<DocumentSnapshot>(
      stream: FirebaseFirestore.instance
          .collection('admin_settings')
          .doc('developers')
          .snapshots(),
      builder: (context, snapshot) {
        Map<String, dynamic> developersData = {};

        if (snapshot.hasData && snapshot.data!.exists) {
          developersData = snapshot.data!.data() as Map<String, dynamic>;
        }

        // Default developer data if not found in Firebase
        final developer1 = developersData['developer1'] ?? {
          'name': 'Salauddin Majumder',
          'description': 'Lead Developer & AI Specialist with expertise in Flutter development and machine learning integration.',
          'website': 'https://salauddin.dev',
        };

        final developer2 = developersData['developer2'] ?? {
          'name': 'Meheron Nesa Surovi',
          'description': 'UI/UX Designer & Frontend Developer specializing in user experience and modern interface design.',
          'website': 'https://surovi.dev',
        };

        return Container(
          constraints: const BoxConstraints(maxWidth: 900),
          padding: const EdgeInsets.all(30),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppColors.primaryGreen.withOpacity(0.05),
                AppColors.accentBlue.withOpacity(0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: AppColors.primaryGreen.withOpacity(0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.primaryGreen.withOpacity(0.1),
                blurRadius: 30,
                offset: const Offset(0, 15),
              ),
            ],
          ),
          child: Column(
            children: [
              // Section Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppColors.primaryGreen,
                          AppColors.accentBlue,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primaryGreen.withOpacity(0.3),
                          blurRadius: 15,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.code_rounded,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 20),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '👨‍💻 ডেভেলপার টিম',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'যারা এই অ্যাপটি তৈরি করেছেন',
                          style: TextStyle(
                            fontSize: 16,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 30),

              // Developers Grid
              LayoutBuilder(
                builder: (context, constraints) {
                  if (constraints.maxWidth > 600) {
                    // Desktop/Tablet layout - side by side
                    return Row(
                      children: [
                        Expanded(
                          child: _buildDeveloperCard(
                            'assets/images/salauddin.jpg',
                            developer1['name'],
                            developer1['description'],
                            developer1['website'],
                          ),
                        ),
                        const SizedBox(width: 20),
                        Expanded(
                          child: _buildDeveloperCard(
                            'assets/images/surovi.jpg',
                            developer2['name'],
                            developer2['description'],
                            developer2['website'],
                          ),
                        ),
                      ],
                    );
                  } else {
                    // Mobile layout - stacked
                    return Column(
                      children: [
                        _buildDeveloperCard(
                          'assets/images/salauddin.jpg',
                          developer1['name'],
                          developer1['description'],
                          developer1['website'],
                        ),
                        const SizedBox(height: 20),
                        _buildDeveloperCard(
                          'assets/images/surovi.jpg',
                          developer2['name'],
                          developer2['description'],
                          developer2['website'],
                        ),
                      ],
                    );
                  }
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // Build individual developer card
  Widget _buildDeveloperCard(String imagePath, String name, String description, String website) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.primaryGreen.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          // Developer Image
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: AppColors.primaryGreen,
                width: 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryGreen.withOpacity(0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: ClipOval(
              child: Image.asset(
                imagePath,
                width: 120,
                height: 120,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  // Fallback to icon if image not found
                  return Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          AppColors.primaryGreen,
                          AppColors.accentBlue,
                        ],
                      ),
                    ),
                    child: const Icon(
                      Icons.person,
                      size: 60,
                      color: Colors.white,
                    ),
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Developer Name
          Text(
            name,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),

          // Developer Description
          Text(
            description,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 20),

          // Know More Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _launchUrl(website),
              icon: const Icon(Icons.language_rounded, size: 18),
              label: const Text(
                'আরও জানুন',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                elevation: 3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build Feedback Section
  Widget _buildFeedbackSection() {
    return Container(
      constraints: const BoxConstraints(maxWidth: 600),
      padding: const EdgeInsets.all(30),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryGreen.withOpacity(0.1),
            AppColors.accentBlue.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.primaryGreen.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryGreen.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primaryGreen.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.feedback_rounded,
                  color: AppColors.primaryGreen,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '💬 আপনার মতামত জানান',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'আপনার অভিজ্ঞতা শেয়ার করুন এবং আমাদের সেবা উন্নত করতে সাহায্য করুন',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 30),

          // Rating Section
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'রেটিং দিন:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: List.generate(5, (index) {
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _feedbackRating = index + 1;
                      });
                    },
                    child: Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: Icon(
                        Icons.star,
                        size: 32,
                        color: index < _feedbackRating
                            ? AppColors.accentOrange
                            : AppColors.textSecondary.withOpacity(0.3),
                      ),
                    ),
                  );
                }),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Name Field
          _buildFeedbackTextField(
            controller: _feedbackNameController,
            label: 'আপনার নাম',
            hint: 'নাম লিখুন',
            icon: Icons.person_outline,
          ),
          const SizedBox(height: 16),

          // Email Field
          _buildFeedbackTextField(
            controller: _feedbackEmailController,
            label: 'ইমেইল (ঐচ্ছিক)',
            hint: '<EMAIL>',
            icon: Icons.email_outlined,
            keyboardType: TextInputType.emailAddress,
          ),
          const SizedBox(height: 16),

          // Message Field
          _buildFeedbackTextField(
            controller: _feedbackMessageController,
            label: 'আপনার মতামত',
            hint: 'আপনার অভিজ্ঞতা, পরামর্শ বা মন্তব্য লিখুন...',
            icon: Icons.message_outlined,
            maxLines: 4,
          ),
          const SizedBox(height: 24),

          // Submit Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isSubmittingFeedback ? null : _submitFeedback,
              icon: _isSubmittingFeedback
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.send_rounded, size: 18),
              label: Text(
                _isSubmittingFeedback ? 'পাঠানো হচ্ছে...' : 'মতামত পাঠান',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build feedback text field
  Widget _buildFeedbackTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, color: AppColors.primaryGreen),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.textSecondary.withOpacity(0.3)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.textSecondary.withOpacity(0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.primaryGreen, width: 2),
            ),
            filled: true,
            fillColor: Colors.white.withOpacity(0.8),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }

  // Submit feedback to Firebase
  Future<void> _submitFeedback() async {
    if (_feedbackNameController.text.trim().isEmpty ||
        _feedbackMessageController.text.trim().isEmpty ||
        _feedbackRating == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('অনুগ্রহ করে সব প্রয়োজনীয় তথ্য পূরণ করুন'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isSubmittingFeedback = true;
    });

    try {
      final user = _authService.currentUser;
      final feedbackData = {
        'name': _feedbackNameController.text.trim(),
        'email': _feedbackEmailController.text.trim(),
        'message': _feedbackMessageController.text.trim(),
        'rating': _feedbackRating,
        'timestamp': FieldValue.serverTimestamp(),
        'userEmail': user?.email ?? 'anonymous',
        'userId': user?.uid ?? 'anonymous',
        'status': 'new',
      };

      await FirebaseFirestore.instance
          .collection('feedback')
          .add(feedbackData);

      // Clear form
      _feedbackNameController.clear();
      _feedbackEmailController.clear();
      _feedbackMessageController.clear();
      setState(() {
        _feedbackRating = 0;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('আপনার মতামত সফলভাবে পাঠানো হয়েছে। ধন্যবাদ!'),
          backgroundColor: AppColors.primaryGreen,
        ),
      );
    } catch (e) {
      debugPrint('Error submitting feedback: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('মতামত পাঠাতে সমস্যা হয়েছে। আবার চেষ্টা করুন।'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isSubmittingFeedback = false;
      });
    }
  }
}

// Custom painter for animated background particles
class ParticlesPainter extends CustomPainter {
  final double animationValue;

  ParticlesPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    // Create floating particles with different colors and sizes
    final particles = [
      {'x': 0.1, 'y': 0.2, 'color': AppColors.primaryGreen.withOpacity(0.1), 'size': 4.0},
      {'x': 0.8, 'y': 0.1, 'color': AppColors.primaryPurple.withOpacity(0.1), 'size': 6.0},
      {'x': 0.3, 'y': 0.7, 'color': AppColors.accentPink.withOpacity(0.08), 'size': 3.0},
      {'x': 0.9, 'y': 0.6, 'color': AppColors.accentBlue.withOpacity(0.1), 'size': 5.0},
      {'x': 0.2, 'y': 0.9, 'color': AppColors.accentOrange.withOpacity(0.08), 'size': 4.5},
      {'x': 0.7, 'y': 0.3, 'color': AppColors.accentCyan.withOpacity(0.1), 'size': 3.5},
    ];

    for (var particle in particles) {
      final x = (particle['x'] as double) * size.width;
      final y = (particle['y'] as double) * size.height +
                (math.sin(animationValue * 2 * math.pi + x / 100) * 20);
      final color = particle['color'] as Color;
      final particleSize = particle['size'] as double;

      paint.color = color;
      canvas.drawCircle(
        Offset(x, y),
        particleSize * (0.8 + 0.4 * math.sin(animationValue * 3 * math.pi + x / 50)),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Custom painter for traveling glow border effect
class TravelingGlowBorderPainter extends CustomPainter {
  final double progress;
  final double borderRadius;

  TravelingGlowBorderPainter({
    required this.progress,
    required this.borderRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final rrect = RRect.fromRectAndRadius(rect, Radius.circular(borderRadius));

    // Create path for the rounded rectangle border
    final path = Path()..addRRect(rrect);

    // Get accurate path metrics
    final pathMetrics = path.computeMetrics().toList();
    if (pathMetrics.isEmpty) return;

    final pathMetric = pathMetrics.first;
    final totalLength = pathMetric.length;

    // Create dynamic gradient colors that cycle smoothly (no position needed)
    final colorProgress = (progress * 2) % 1.0; // Smooth color cycling
    final primaryColor = Color.lerp(
      AppColors.primaryGreen,
      AppColors.primaryPurple,
      colorProgress,
    )!;
    final accentColor = Color.lerp(
      AppColors.accentPink,
      AppColors.accentOrange,
      (colorProgress + 0.5) % 1.0,
    )!;

    // Create complete border path (100% perimeter, stationary)
    final segmentPath = pathMetric.extractPath(0, totalLength);

    // Create multiple glow layers with reduced intensity
    final glowLayers = [
      {'width': 12.0, 'opacity': 0.08, 'blur': 8.0},
      {'width': 8.0, 'opacity': 0.12, 'blur': 6.0},
      {'width': 6.0, 'opacity': 0.18, 'blur': 3.0},
      {'width': 3.0, 'opacity': 0.25, 'blur': 1.5},
    ];

    // Draw glow layers from largest to smallest
    for (final layer in glowLayers) {
      final glowPaint = Paint()
        ..style = PaintingStyle.stroke
        ..strokeWidth = layer['width'] as double
        ..color = primaryColor.withOpacity(layer['opacity'] as double)
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, layer['blur'] as double);

      canvas.drawPath(segmentPath, glowPaint);
    }

    // Draw the main line with reduced gradient intensity
    final mainPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..shader = LinearGradient(
        colors: [
          primaryColor.withOpacity(0.15),
          accentColor.withOpacity(0.4),
          primaryColor.withOpacity(0.5),
          accentColor.withOpacity(0.4),
          primaryColor.withOpacity(0.15),
        ],
        stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
      ).createShader(rect);

    canvas.drawPath(segmentPath, mainPaint);

    // Add a subtle core line
    final corePaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.8
      ..color = Colors.white.withOpacity(0.3);

    canvas.drawPath(segmentPath, corePaint);
  }

  @override
  bool shouldRepaint(TravelingGlowBorderPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}

// Custom painter for shimmer effect on send button
class ShimmerEffectPainter extends CustomPainter {
  final double progress;
  final double borderRadius;

  ShimmerEffectPainter({
    required this.progress,
    required this.borderRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final rrect = RRect.fromRectAndRadius(rect, Radius.circular(borderRadius));

    // Create shimmer gradient that moves across the button
    final shimmerPosition = progress * (size.width + 100);

    final shimmerPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
        colors: [
          Colors.transparent,
          Colors.white.withOpacity(0.05),
          Colors.white.withOpacity(0.15),
          Colors.white.withOpacity(0.05),
          Colors.transparent,
        ],
        stops: const [0.0, 0.3, 0.5, 0.7, 1.0],
        transform: GradientRotation(math.pi / 6), // Diagonal shimmer
      ).createShader(Rect.fromLTWH(
        shimmerPosition - 50,
        0,
        100,
        size.height,
      ));

    canvas.clipRRect(rrect);
    canvas.drawRect(
      Rect.fromLTWH(shimmerPosition - 50, 0, 100, size.height),
      shimmerPaint,
    );
  }

  @override
  bool shouldRepaint(ShimmerEffectPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}

// Custom painter for loading ring animation
class LoadingRingPainter extends CustomPainter {
  final double progress;

  LoadingRingPainter({required this.progress});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 2;

    // Draw animated arc
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round
      ..shader = LinearGradient(
        colors: [
          Colors.white.withOpacity(0.8),
          Colors.white.withOpacity(0.3),
        ],
      ).createShader(Rect.fromCircle(center: center, radius: radius));

    final sweepAngle = progress * 2 * math.pi * 0.75; // 75% of circle
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2, // Start from top
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(LoadingRingPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}

// Custom painter for particle effects around send button
class ParticleEffectPainter extends CustomPainter {
  final double progress;
  final double pulseValue;

  ParticleEffectPainter({
    required this.progress,
    required this.pulseValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Create floating particles around the button
    final particleCount = 8;
    final paint = Paint()..style = PaintingStyle.fill;

    for (int i = 0; i < particleCount; i++) {
      final angle = (i / particleCount) * 2 * math.pi + (progress * 2 * math.pi);
      final particleRadius = radius + 15 + (10 * math.sin(progress * 4 * math.pi + i));

      final x = center.dx + math.cos(angle) * particleRadius;
      final y = center.dy + math.sin(angle) * particleRadius;

      // Particle color cycles through the gradient
      final colorProgress = (progress + i / particleCount) % 1.0;
      final particleColor = Color.lerp(
        AppColors.primaryGreen,
        AppColors.primaryPurple,
        colorProgress,
      )!;

      paint.color = particleColor.withOpacity(0.4 + (0.2 * pulseValue));

      final particleSize = 1.5 + (1.0 * pulseValue);
      canvas.drawCircle(Offset(x, y), particleSize, paint);

      // Add subtle glow effect
      paint.color = particleColor.withOpacity(0.1);
      canvas.drawCircle(Offset(x, y), particleSize * 1.5, paint);
    }
  }

  @override
  bool shouldRepaint(ParticleEffectPainter oldDelegate) {
    return oldDelegate.progress != progress || oldDelegate.pulseValue != pulseValue;
  }
}

// Custom painter for flowing border effect from input to send button
class FlowingBorderPainter extends CustomPainter {
  final double inputProgress;
  final double buttonProgress;
  final double pulseValue;
  final bool hasText;

  FlowingBorderPainter({
    required this.inputProgress,
    required this.buttonProgress,
    required this.pulseValue,
    required this.hasText,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Create the flowing border effect that connects input to button
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Calculate the flow position based on input progress
    // The flow should appear to come from the left (input field direction)
    final flowAngle = (inputProgress * 2 * math.pi) + (buttonProgress * math.pi);

    // Create multiple flowing segments for black hole effect
    for (int i = 0; i < 4; i++) {
      final segmentAngle = flowAngle + (i * math.pi / 2);
      final segmentLength = math.pi / 3; // Length of each flowing segment

      // Calculate opacity and width based on pulse and text presence
      final baseOpacity = hasText ? 0.8 : 0.4;
      final opacity = baseOpacity * (0.7 + 0.3 * pulseValue) * (1.0 - i * 0.2);
      final strokeWidth = (3.0 + pulseValue * 2.0) * (1.0 - i * 0.3);

      // Color cycling for each segment
      final colors = [
        AppColors.primaryGreen,
        AppColors.primaryPurple,
        AppColors.accentPink,
        AppColors.accentOrange,
      ];
      final color = colors[i % colors.length];

      paint
        ..color = color.withOpacity(opacity)
        ..strokeWidth = strokeWidth;

      // Create the flowing arc segment
      final startAngle = segmentAngle - segmentLength / 2;
      final sweepAngle = segmentLength;

      // Draw the main flowing segment
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius - strokeWidth / 2),
        startAngle,
        sweepAngle,
        false,
        paint,
      );

      // Add inner glow effect
      paint
        ..color = color.withOpacity(opacity * 0.5)
        ..strokeWidth = strokeWidth * 0.6;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius - strokeWidth / 2),
        startAngle,
        sweepAngle,
        false,
        paint,
      );

      // Add outer glow effect
      paint
        ..color = color.withOpacity(opacity * 0.3)
        ..strokeWidth = strokeWidth * 1.5;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius - strokeWidth / 2),
        startAngle,
        sweepAngle,
        false,
        paint,
      );
    }

    // Add connecting particles that flow from input direction
    if (hasText) {
      final particlePaint = Paint()..style = PaintingStyle.fill;

      for (int i = 0; i < 6; i++) {
        final particleAngle = flowAngle + (i * math.pi / 3);
        final particleDistance = radius + (math.sin(inputProgress * 4 * math.pi + i) * 8);

        final particleX = center.dx + math.cos(particleAngle) * particleDistance;
        final particleY = center.dy + math.sin(particleAngle) * particleDistance;

        final particleSize = (2.0 + pulseValue) * (1.0 - i * 0.1);
        final particleOpacity = (0.8 - i * 0.1) * pulseValue;

        final particleColor = [
          AppColors.primaryGreen,
          AppColors.primaryPurple,
          AppColors.accentPink,
        ][i % 3];

        particlePaint.color = particleColor.withOpacity(particleOpacity);
        canvas.drawCircle(Offset(particleX, particleY), particleSize, particlePaint);
      }
    }
  }

  @override
  bool shouldRepaint(FlowingBorderPainter oldDelegate) {
    return oldDelegate.inputProgress != inputProgress ||
           oldDelegate.buttonProgress != buttonProgress ||
           oldDelegate.pulseValue != pulseValue ||
           oldDelegate.hasText != hasText;
  }
}

// Magical Border Painter for thin glowing screen border during AI response
class MagicalBorderPainter extends CustomPainter {
  final double glowProgress;
  final double rotationProgress;
  final double pulseValue;

  MagicalBorderPainter({
    required this.glowProgress,
    required this.rotationProgress,
    required this.pulseValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Create single thin glowing border
    final borderRect = Rect.fromLTWH(2, 2, size.width - 4, size.height - 4);

    // Animated gradient colors
    final colors = [
      AppColors.primaryGreen,
      AppColors.primaryPurple,
      AppColors.accentOrange,
      const Color(0xFF00D4FF), // Cyan
      const Color(0xFFFF6B9D), // Pink
    ];

    // Create flowing gradient effect
    final gradientColors = <Color>[];
    final gradientStops = <double>[];

    for (int j = 0; j < colors.length; j++) {
      final progress = (glowProgress + (j * 0.2)) % 1.0;
      gradientColors.add(colors[j]);
      gradientStops.add(progress);
    }

    // Sort stops to ensure proper gradient
    final sortedData = List.generate(gradientColors.length, (index) => {
      'color': gradientColors[index],
      'stop': gradientStops[index],
    });
    sortedData.sort((a, b) => (a['stop'] as double).compareTo(b['stop'] as double));

    final sortedColors = sortedData.map((e) => e['color'] as Color).toList();
    final sortedStops = sortedData.map((e) => e['stop'] as double).toList();

    // Create sweeping gradient
    final gradient = SweepGradient(
      center: Alignment.center,
      startAngle: rotationProgress * 2 * math.pi,
      endAngle: (rotationProgress + 1) * 2 * math.pi,
      colors: sortedColors,
      stops: sortedStops,
    );

    // Create thin glowing border paint
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..shader = gradient.createShader(borderRect)
      ..strokeWidth = 2 + (1 * pulseValue); // Thin border that pulses slightly

    // Draw the thin border with glow effect using multiple passes
    for (int i = 0; i < 3; i++) {
      final glowPaint = Paint()
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round
        ..shader = gradient.createShader(borderRect)
        ..strokeWidth = (2 + (1 * pulseValue)) + (i * 2)
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 2 + (i * 2));

      // Draw rounded rectangle border
      final rrect = RRect.fromRectAndRadius(
        borderRect,
        const Radius.circular(8),
      );

      canvas.drawRRect(rrect, glowPaint);
    }

    // Draw the main thin border on top
    final rrect = RRect.fromRectAndRadius(
      borderRect,
      const Radius.circular(8),
    );
    canvas.drawRRect(rrect, paint);
  }

  @override
  bool shouldRepaint(MagicalBorderPainter oldDelegate) {
    return oldDelegate.glowProgress != glowProgress ||
           oldDelegate.rotationProgress != rotationProgress ||
           oldDelegate.pulseValue != pulseValue;
  }
}