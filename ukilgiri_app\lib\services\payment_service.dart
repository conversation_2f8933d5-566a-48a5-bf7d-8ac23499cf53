import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../config/payment_config.dart';

class PaymentService {

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  String? _accessToken;
  DateTime? _tokenExpiry;

  // Get access token from bKash
  Future<String?> _getAccessToken() async {
    try {
      // Check if token is still valid
      if (_accessToken != null &&
          _tokenExpiry != null &&
          DateTime.now().isBefore(_tokenExpiry!)) {
        return _accessToken;
      }

      final response = await http.post(
        Uri.parse('${PaymentConfig.baseUrl}/tokenized/checkout/token/grant'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'username': PaymentConfig.bkashUsername,
          'password': PaymentConfig.bkashPassword,
        },
        body: json.encode({
          'app_key': PaymentConfig.bkashAppKey,
          'app_secret': PaymentConfig.bkashAppSecret,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _accessToken = data['id_token'];
        _tokenExpiry = DateTime.now().add(const Duration(hours: 1));
        return _accessToken;
      } else {
        debugPrint('Failed to get bKash token: ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Error getting bKash token: $e');
      return null;
    }
  }

  // Create payment for premium subscription
  Future<Map<String, dynamic>?> createPayment({
    required String userId,
    required double amount,
    required String merchantInvoiceNumber,
  }) async {
    try {
      final token = await _getAccessToken();
      if (token == null) {
        throw 'bKash token পেতে সমস্যা হয়েছে';
      }

      final response = await http.post(
        Uri.parse('${PaymentConfig.baseUrl}/tokenized/checkout/create'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'authorization': token,
          'x-app-key': PaymentConfig.bkashAppKey,
        },
        body: json.encode({
          'mode': '0011',
          'payerReference': userId,
          'callbackURL': PaymentConfig.callbackUrl,
          'amount': amount.toString(),
          'currency': 'BDT',
          'intent': 'sale',
          'merchantInvoiceNumber': merchantInvoiceNumber,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        // Save payment record to Firestore
        await _savePaymentRecord(
          userId: userId,
          paymentId: data['paymentID'],
          amount: amount,
          status: 'created',
          merchantInvoiceNumber: merchantInvoiceNumber,
        );

        return data;
      } else {
        debugPrint('Failed to create bKash payment: ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Error creating bKash payment: $e');
      return null;
    }
  }

  // Execute payment
  Future<Map<String, dynamic>?> executePayment({
    required String paymentId,
  }) async {
    try {
      final token = await _getAccessToken();
      if (token == null) {
        throw 'bKash token পেতে সমস্যা হয়েছে';
      }

      final response = await http.post(
        Uri.parse('${PaymentConfig.baseUrl}/tokenized/checkout/execute'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'authorization': token,
          'x-app-key': PaymentConfig.bkashAppKey,
        },
        body: json.encode({
          'paymentID': paymentId,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        // Update payment status
        await _updatePaymentStatus(paymentId, 'completed', data);

        return data;
      } else {
        debugPrint('Failed to execute bKash payment: ${response.body}');
        return null;
      }
    } catch (e) {
      debugPrint('Error executing bKash payment: $e');
      return null;
    }
  }

  // Save payment record to Firestore
  Future<void> _savePaymentRecord({
    required String userId,
    required String paymentId,
    required double amount,
    required String status,
    required String merchantInvoiceNumber,
  }) async {
    try {
      await _firestore.collection('payments').doc(paymentId).set({
        'userId': userId,
        'paymentId': paymentId,
        'amount': amount,
        'currency': 'BDT',
        'status': status,
        'merchantInvoiceNumber': merchantInvoiceNumber,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'type': 'premium_subscription',
      });
    } catch (e) {
      debugPrint('Error saving payment record: $e');
    }
  }

  // Update payment status
  Future<void> _updatePaymentStatus(
    String paymentId,
    String status,
    Map<String, dynamic> paymentData,
  ) async {
    try {
      await _firestore.collection('payments').doc(paymentId).update({
        'status': status,
        'updatedAt': FieldValue.serverTimestamp(),
        'paymentData': paymentData,
      });

      // If payment is completed, activate premium for user
      if (status == 'completed') {
        final paymentDoc = await _firestore.collection('payments').doc(paymentId).get();
        final userId = paymentDoc.data()?['userId'];

        if (userId != null) {
          await _activatePremium(userId);
        }
      }
    } catch (e) {
      debugPrint('Error updating payment status: $e');
    }
  }

  // Activate premium subscription for user
  Future<void> _activatePremium(String userId) async {
    try {
      final premiumExpiry = DateTime.now().add(const Duration(days: 30)); // 30 days premium

      await _firestore.collection('users').doc(userId).update({
        'isPremium': true,
        'premiumActivatedAt': FieldValue.serverTimestamp(),
        'premiumExpiresAt': Timestamp.fromDate(premiumExpiry),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error activating premium: $e');
    }
  }

  // Check if user has active premium
  Future<bool> isUserPremium(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      final userData = doc.data();

      if (userData == null) return false;

      final isPremium = userData['isPremium'] ?? false;
      final premiumExpiresAt = userData['premiumExpiresAt'] as Timestamp?;

      if (!isPremium || premiumExpiresAt == null) return false;

      // Check if premium has expired
      return DateTime.now().isBefore(premiumExpiresAt.toDate());
    } catch (e) {
      debugPrint('Error checking premium status: $e');
      return false;
    }
  }

  // Get user's premium info
  Future<Map<String, dynamic>?> getUserPremiumInfo(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      final userData = doc.data();

      if (userData == null) return null;

      final isPremium = userData['isPremium'] ?? false;
      final premiumExpiresAt = userData['premiumExpiresAt'] as Timestamp?;
      final premiumActivatedAt = userData['premiumActivatedAt'] as Timestamp?;

      return {
        'isPremium': isPremium,
        'premiumExpiresAt': premiumExpiresAt?.toDate(),
        'premiumActivatedAt': premiumActivatedAt?.toDate(),
        'daysRemaining': premiumExpiresAt != null
          ? DateTime.now().difference(premiumExpiresAt.toDate()).inDays.abs()
          : 0,
      };
    } catch (e) {
      debugPrint('Error getting premium info: $e');
      return null;
    }
  }

  // Generate unique merchant invoice number
  String generateMerchantInvoiceNumber() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'UKIL_PREMIUM_$timestamp';
  }
}
