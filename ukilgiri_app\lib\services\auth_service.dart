import 'dart:convert';
import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  GoogleSignIn? _googleSignIn;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Lazy initialization of Google Sign-In to avoid type casting issues
  GoogleSignIn get googleSignIn {
    if (_googleSignIn == null) {
      try {
        _googleSignIn = GoogleSignIn(
          scopes: ['email', 'profile'],
        );
        debugPrint('Google Sign-In initialized successfully');
      } catch (e) {
        debugPrint('Error initializing Google Sign-In: $e');
        // Create a basic instance without scopes if there's an issue
        _googleSignIn = GoogleSignIn();
      }
    }
    return _googleSignIn!;
  }

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Validate Gmail email
  bool _isGmailEmail(String email) {
    return email.toLowerCase().endsWith('@gmail.com');
  }

  // Sign up with email and password
  Future<UserCredential?> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
  }) async {
    // Check if email is Gmail
    if (!_isGmailEmail(email)) {
      throw 'শুধুমাত্র Gmail ইমেইল ঠিকানা ব্যবহার করুন (<EMAIL>)';
    }

    try {
      UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Update display name
      await result.user?.updateDisplayName(name);

      // Send email verification
      await result.user?.sendEmailVerification();

      // Create user document in Firestore
      await _createUserDocument(result.user!, name);

      return result;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw 'একটি অপ্রত্যাশিত ত্রুটি ঘটেছে। আবার চেষ্টা করুন।';
    }
  }

  // Sign in with email and password
  Future<UserCredential?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    // Check if email is Gmail
    if (!_isGmailEmail(email)) {
      throw 'শুধুমাত্র Gmail ইমেইল ঠিকানা ব্যবহার করুন (<EMAIL>)';
    }

    UserCredential? result;

    try {
      debugPrint('Attempting to sign in with email: $email');

      // First, try to sign in
      result = await _auth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      debugPrint('Firebase sign in successful for user: ${result.user?.uid}');

      // Only update last sign in if the main sign in was successful
      if (result.user != null) {
        debugPrint('Updating last sign in...');

        // Use a simpler approach for updating last sign in
        try {
          await _firestore.collection('users').doc(result.user!.uid).set({
            'lastSignIn': FieldValue.serverTimestamp(),
            'lastLoginAt': FieldValue.serverTimestamp(),
          }, SetOptions(merge: true));

          debugPrint('Last sign in updated successfully');
        } catch (updateError) {
          debugPrint('Error updating last sign in (non-critical): $updateError');
          // Don't throw error for this, just log it
        }
      }

      return result;
    } on FirebaseAuthException catch (e) {
      debugPrint('Firebase Auth Error: ${e.code} - ${e.message}');
      throw _handleAuthException(e);
    } catch (e) {
      debugPrint('Unexpected error during sign in: $e');
      debugPrint('Error type: ${e.runtimeType}');

      // Handle specific type casting errors that might occur
      if (e.toString().contains('PigeonUserDetails') ||
          e.toString().contains('type cast') ||
          e.toString().contains('List<Object?>') ||
          e.toString().contains('_TypeError')) {

        // This is likely a Google Sign-In plugin issue, but the user was actually signed in
        // Let's check if the user is actually signed in despite the error
        if (_auth.currentUser != null) {
          debugPrint('User is actually signed in despite the error, returning success');
          return result;
        } else {
          throw 'সাইন ইন সফল হয়েছে কিন্তু একটি ছোট সমস্যা হয়েছে। অ্যাপ পুনরায় চালু করুন।';
        }
      } else if (e.toString().contains('network')) {
        throw 'নেটওয়ার্ক সংযোগ সমস্যা। ইন্টারনেট সংযোগ পরীক্ষা করুন।';
      } else if (e.toString().contains('timeout')) {
        throw 'সংযোগ টাইমআউট। আবার চেষ্টা করুন।';
      } else if (e.toString().contains('permission')) {
        throw 'অনুমতি সমস্যা। অ্যাপ পুনরায় চালু করুন।';
      } else {
        throw 'সাইন ইন করতে সমস্যা হয়েছে: ${e.toString()}';
      }
    }
  }

  // Sign in with Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      debugPrint('Starting Google Sign-In process...');

      // Sign out first to ensure clean state
      try {
        await googleSignIn.signOut();
      } catch (signOutError) {
        debugPrint('Error during sign out (ignoring): $signOutError');
        // Ignore sign out errors as they don't affect the main flow
      }

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await googleSignIn.signIn();

      // If the user cancels the sign-in process
      if (googleUser == null) {
        debugPrint('Google Sign-In cancelled by user');
        throw 'Google সাইন ইন বাতিল করা হয়েছে।';
      }

      debugPrint('Google user obtained: ${googleUser.email}');

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      debugPrint('Google authentication obtained');

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      debugPrint('Firebase credential created');

      // Sign in to Firebase with the Google credential
      UserCredential result = await _auth.signInWithCredential(credential);

      debugPrint('Firebase sign-in successful');

      // Create user document if new user
      if (result.additionalUserInfo?.isNewUser == true) {
        debugPrint('New user, creating document...');
        await _createUserDocument(result.user!, googleUser.displayName ?? 'ব্যবহারকারী');
      } else {
        debugPrint('Existing user, updating last sign in...');
        // Update last sign in for existing users
        try {
          await updateLastSignIn();
        } catch (updateError) {
          debugPrint('Error updating last sign in: $updateError');
          // Don't throw error for this, just log it
        }
      }

      return result;
    } on FirebaseAuthException catch (e) {
      debugPrint('Firebase Auth Error: ${e.code} - ${e.message}');
      switch (e.code) {
        case 'account-exists-with-different-credential':
          throw 'এই ইমেইল ঠিকানা অন্য পদ্ধতিতে নিবন্ধিত। অন্য উপায়ে সাইন ইন করুন।';
        case 'invalid-credential':
          throw 'Google সাইন ইন তথ্য সঠিক নয়।';
        case 'operation-not-allowed':
          throw 'Google সাইন ইন সক্রিয় নয়। অ্যাডমিনের সাথে যোগাযোগ করুন।';
        case 'user-disabled':
          throw 'এই অ্যাকাউন্টটি নিষ্ক্রিয় করা হয়েছে।';
        default:
          throw 'Google সাইন ইন করতে সমস্যা হয়েছে: ${e.message}';
      }
    } catch (e) {
      debugPrint('Google Sign-In Error: $e');
      debugPrint('Error type: ${e.runtimeType}');

      // Handle specific type casting errors that are common with Google Sign-In plugin
      if (e.toString().contains('PigeonUserDetails') ||
          e.toString().contains('type cast') ||
          e.toString().contains('List<Object?>') ||
          e.toString().contains('_TypeError')) {

        debugPrint('Type casting error detected in Google Sign-In');

        // Check if user was actually signed in despite the error
        await Future.delayed(const Duration(milliseconds: 500));

        if (_auth.currentUser != null) {
          debugPrint('User is actually signed in despite the error');
          // Create a mock UserCredential-like result
          return null; // Return null but user is signed in
        } else {
          throw 'Google সাইন ইন প্লাগইন সমস্যা। দয়া করে ইমেইল/পাসওয়ার্ড দিয়ে সাইন ইন করুন।';
        }
      } else if (e.toString().contains('network')) {
        throw 'নেটওয়ার্ক সংযোগ সমস্যা। ইন্টারনেট সংযোগ পরীক্ষা করুন।';
      } else if (e.toString().contains('sign_in_canceled') || e.toString().contains('cancelled')) {
        throw 'Google সাইন ইন বাতিল করা হয়েছে।';
      } else {
        throw 'Google সাইন ইন করতে সমস্যা হয়েছে। ইমেইল/পাসওয়ার্ড দিয়ে সাইন ইন করার চেষ্টা করুন।';
      }
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      // Only sign out from Google if it was initialized
      if (_googleSignIn != null) {
        await _googleSignIn!.signOut();
      }
      await _auth.signOut();
    } catch (e) {
      throw 'সাইন আউট করতে সমস্যা হয়েছে।';
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    // Check if email is Gmail
    if (!_isGmailEmail(email)) {
      throw 'শুধুমাত্র Gmail ইমেইল ঠিকানা ব্যবহার করুন (<EMAIL>)';
    }

    try {
      await _auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw 'পাসওয়ার্ড রিসেট ইমেইল পাঠাতে সমস্যা হয়েছে।';
    }
  }

  // Send email verification
  Future<void> sendEmailVerification() async {
    try {
      User? user = _auth.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
      }
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw 'ইমেইল ভেরিফিকেশন পাঠাতে সমস্যা হয়েছে।';
    }
  }

  // Check if email is verified
  bool get isEmailVerified {
    return _auth.currentUser?.emailVerified ?? false;
  }

  // Reload user to get updated email verification status
  Future<void> reloadUser() async {
    await _auth.currentUser?.reload();
  }

  // Check if current user is admin
  bool isAdmin() {
    final user = _auth.currentUser;
    if (user == null) {
      debugPrint('isAdmin: No current user');
      return false;
    }

    final userEmail = user.email?.toLowerCase();
    final isAdminUser = userEmail == '<EMAIL>';
    debugPrint('isAdmin: User email: $userEmail, Is admin: $isAdminUser, Email verified: ${user.emailVerified}');

    // Check if the user's email is the admin email
    return isAdminUser;
  }
  // Create user document in Firestore
  Future<void> _createUserDocument(User user, String name) async {
    try {
      await _firestore.collection('users').doc(user.uid).set({
        'uid': user.uid,
        'name': name,
        'email': user.email,
        'createdAt': FieldValue.serverTimestamp(),
        'lastSignIn': FieldValue.serverTimestamp(),
        'chatHistory': [],
        'totalCharactersUsed': 0, // Initialize character count
        'preferences': {
          'language': 'bn',
          'notifications': true,
        },
      });
    } catch (e) {
      // Log error in production, you might want to use a proper logging service
      debugPrint('Error creating user document: $e');
    }
  }

  // Update user last sign in
  Future<void> updateLastSignIn() async {
    try {
      if (currentUser != null) {
        debugPrint('Updating last sign in for user: ${currentUser!.uid}');

        // First check if user document exists
        final userDoc = await _firestore.collection('users').doc(currentUser!.uid).get();

        if (!userDoc.exists) {
          debugPrint('User document does not exist, creating it...');
          await _createUserDocument(currentUser!, currentUser!.displayName ?? 'ব্যবহারকারী');
        } else {
          debugPrint('User document exists, updating last sign in...');
          await _firestore.collection('users').doc(currentUser!.uid).update({
            'lastSignIn': FieldValue.serverTimestamp(),
            'lastLoginAt': FieldValue.serverTimestamp(), // Also update lastLoginAt for consistency
          });
        }

        debugPrint('Last sign in update completed');
      } else {
        debugPrint('No current user to update last sign in');
      }
    } catch (e) {
      debugPrint('Error updating last sign in: $e');
      // Don't throw error, just log it
    }
  }

  // Get user data from Firestore
  Future<Map<String, dynamic>?> getUserData() async {
    try {
      if (currentUser != null) {
        DocumentSnapshot doc = await _firestore.collection('users').doc(currentUser!.uid).get();
        return doc.data() as Map<String, dynamic>?;
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user data: $e');
      return null;
    }
  }

  // Handle Firebase Auth exceptions
  String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'weak-password':
        return 'পাসওয়ার্ড খুবই দুর্বল। অন্তত ৬টি অক্ষর ব্যবহার করুন।';
      case 'email-already-in-use':
        return 'এই ইমেইল ঠিকানা ইতিমধ্যে ব্যবহৃত হয়েছে।';
      case 'invalid-email':
        return 'ইমেইল ঠিকানা সঠিক নয়।';
      case 'user-not-found':
        return 'এই ইমেইল ঠিকানায় কোনো অ্যাকাউন্ট পাওয়া যায়নি।';
      case 'wrong-password':
        return 'পাসওয়ার্ড ভুল। আবার চেষ্টা করুন।';
      case 'user-disabled':
        return 'এই অ্যাকাউন্টটি নিষ্ক্রিয় করা হয়েছে।';
      case 'too-many-requests':
        return 'অনেকবার চেষ্টা করেছেন। কিছুক্ষণ পর আবার চেষ্টা করুন।';
      case 'operation-not-allowed':
        return 'এই অপারেশন অনুমোদিত নয়।';
      case 'invalid-credential':
        return 'প্রদত্ত তথ্য সঠিক নয়।';
      default:
        return 'একটি ত্রুটি ঘটেছে: ${e.message}';
    }
  }



  // Update user profile
  Future<void> updateUserProfile({
    String? name,
    String? phone,
    String? address,
    String? profession,
  }) async {
    try {
      if (currentUser != null) {
        // First ensure the user document exists
        await _ensureUserDocumentExists();

        Map<String, dynamic> updateData = {
          'updatedAt': FieldValue.serverTimestamp(),
        };

        if (name != null && name.isNotEmpty) {
          updateData['name'] = name;
          // Try to update Firebase Auth display name (optional)
          try {
            await currentUser!.updateDisplayName(name);
          } catch (e) {
            debugPrint('Warning: Could not update Firebase Auth display name: $e');
            // Continue with profile update even if display name update fails
          }
        }

        if (phone != null) updateData['phone'] = phone;
        if (address != null) updateData['address'] = address;
        if (profession != null) updateData['profession'] = profession;

        // Use set with merge option to handle cases where document might not exist
        await _firestore.collection('users').doc(currentUser!.uid).set(
          updateData,
          SetOptions(merge: true),
        );
      }
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      throw 'প্রোফাইল আপডেট করতে সমস্যা হয়েছে।';
    }
  }

  // Ensure user document exists
  Future<void> _ensureUserDocumentExists() async {
    try {
      if (currentUser != null) {
        DocumentSnapshot doc = await _firestore.collection('users').doc(currentUser!.uid).get();

        if (!doc.exists) {
          // Create the document if it doesn't exist
          await _createUserDocument(currentUser!, currentUser!.displayName ?? 'ব্যবহারকারী');
        }
      }
    } catch (e) {
      debugPrint('Error ensuring user document exists: $e');
    }
  }

  // Calculate total characters from text
  int calculateCharacters(String text) {
    if (text.isEmpty) return 0;
    return text.length;
  }

  // Update user's total character count
  Future<void> updateCharacterCount(int charactersUsed) async {
    try {
      if (currentUser != null) {
        // Ensure user document exists first
        await _ensureUserDocumentExists();

        DocumentSnapshot doc = await _firestore.collection('users').doc(currentUser!.uid).get();
        Map<String, dynamic>? userData = doc.data() as Map<String, dynamic>?;

        int currentTotalCharacters = userData?['totalCharactersUsed'] ?? 0;
        int newTotalCharacters = currentTotalCharacters + charactersUsed;

        await _firestore.collection('users').doc(currentUser!.uid).update({
          'totalCharactersUsed': newTotalCharacters,
          'lastCharacterUpdate': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      debugPrint('Error updating character count: $e');
      // Don't throw error for character counting as it's not critical
    }
  }

  // Count AI response (called every time user gets an AI response)
  Future<void> countAIResponse() async {
    try {
      if (currentUser != null) {
        // Ensure user document exists first
        await _ensureUserDocumentExists();

        // Get current user data to check daily chat count
        DocumentSnapshot doc = await _firestore.collection('users').doc(currentUser!.uid).get();
        Map<String, dynamic>? userData = doc.data() as Map<String, dynamic>?;

        // Get current date string (YYYY-MM-DD) - consistent format
        final now = DateTime.now();
        String today = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

        // Initialize counters
        int totalLifetimeChats = (userData?['totalLifetimeChats'] ?? 0) + 1;
        Map<String, dynamic> dailyChats = Map<String, dynamic>.from(userData?['dailyChats'] ?? {});

        // Update daily chat count
        dailyChats[today] = (dailyChats[today] ?? 0) + 1;

        await _firestore.collection('users').doc(currentUser!.uid).update({
          'lastChatAt': FieldValue.serverTimestamp(),
          'totalLifetimeChats': totalLifetimeChats, // Never decreases
          'dailyChats': dailyChats, // Tracks daily counts
          'lastActivityDate': today, // Track last activity date
        });
      }
    } catch (e) {
      debugPrint('Error counting AI response: $e');
      // Don't throw error for counting as it's not critical
    }
  }

  // Save chat conversation to user's Firestore document (without counting)
  Future<void> saveChatConversation(Map<String, dynamic> conversation) async {
    try {
      if (currentUser != null) {
        // Ensure user document exists first
        await _ensureUserDocumentExists();

        // Get current date string (YYYY-MM-DD) - consistent format
        final now = DateTime.now();
        String today = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

        // Add timestamp to conversation for consistent tracking
        Map<String, dynamic> conversationWithTimestamp = Map<String, dynamic>.from(conversation);
        conversationWithTimestamp['timestamp'] = Timestamp.now();
        conversationWithTimestamp['dateString'] = today;

        await _firestore.collection('users').doc(currentUser!.uid).update({
          'chatHistory': FieldValue.arrayUnion([conversationWithTimestamp]),
        });
      }
    } catch (e) {
      debugPrint('Error saving chat conversation: $e');
      throw 'চ্যাট সংরক্ষণ করতে সমস্যা হয়েছে।';
    }
  }

  // Update existing chat conversation
  Future<void> updateChatConversation(String conversationId, Map<String, dynamic> updatedConversation) async {
    try {
      if (currentUser != null) {
        DocumentSnapshot doc = await _firestore.collection('users').doc(currentUser!.uid).get();
        Map<String, dynamic>? userData = doc.data() as Map<String, dynamic>?;

        List<dynamic> chatHistory = userData?['chatHistory'] ?? [];

        // Find and update the conversation
        for (int i = 0; i < chatHistory.length; i++) {
          if (chatHistory[i]['id'] == conversationId) {
            chatHistory[i] = updatedConversation;
            break;
          }
        }

        await _firestore.collection('users').doc(currentUser!.uid).update({
          'chatHistory': chatHistory,
          'lastChatAt': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      debugPrint('Error updating chat conversation: $e');
      throw 'চ্যাট আপডেট করতে সমস্যা হয়েছে।';
    }
  }

  // Get user's chat history
  Future<List<Map<String, dynamic>>> getUserChatHistory() async {
    try {
      if (currentUser != null) {
        DocumentSnapshot doc = await _firestore.collection('users').doc(currentUser!.uid).get();
        Map<String, dynamic>? userData = doc.data() as Map<String, dynamic>?;

        List<dynamic> chatHistory = userData?['chatHistory'] ?? [];
        return chatHistory.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e) {
      debugPrint('Error getting user chat history: $e');
      return [];
    }
  }

  // Get today's chat count in real-time
  Future<int> getTodaysChatCount() async {
    try {
      if (currentUser != null) {
        // Ensure user document exists first
        await _ensureUserDocumentExists();

        DocumentSnapshot doc = await _firestore.collection('users').doc(currentUser!.uid).get();
        Map<String, dynamic>? userData = doc.data() as Map<String, dynamic>?;

        // Get today's date in consistent format
        final now = DateTime.now();
        String today = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

        Map<String, dynamic> dailyChats = Map<String, dynamic>.from(userData?['dailyChats'] ?? {});
        return dailyChats[today] ?? 0;
      }
      return 0;
    } catch (e) {
      debugPrint('Error getting today\'s chat count: $e');
      return 0;
    }
  }

  // Stream for real-time today's chat count
  Stream<int> getTodaysChatCountStream() {
    if (currentUser == null) {
      return Stream.value(0);
    }

    return _firestore
        .collection('users')
        .doc(currentUser!.uid)
        .snapshots()
        .map((snapshot) {
      if (!snapshot.exists) return 0;

      final userData = snapshot.data() as Map<String, dynamic>?;
      if (userData == null) return 0;

      // Get today's date in consistent format
      final now = DateTime.now();
      String today = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

      Map<String, dynamic> dailyChats = Map<String, dynamic>.from(userData['dailyChats'] ?? {});
      return dailyChats[today] ?? 0;
    });
  }

  // Delete a chat conversation
  Future<void> deleteChatConversation(String conversationId) async {
    try {
      if (currentUser != null) {
        DocumentSnapshot doc = await _firestore.collection('users').doc(currentUser!.uid).get();
        Map<String, dynamic>? userData = doc.data() as Map<String, dynamic>?;

        List<dynamic> chatHistory = userData?['chatHistory'] ?? [];
        chatHistory.removeWhere((conversation) => conversation['id'] == conversationId);

        await _firestore.collection('users').doc(currentUser!.uid).update({
          'chatHistory': chatHistory,
        });
      }
    } catch (e) {
      debugPrint('Error deleting chat conversation: $e');
      throw 'চ্যাট মুছতে সমস্যা হয়েছে।';
    }
  }

  // Clear all chat history for user
  Future<void> clearAllChatHistory() async {
    try {
      if (currentUser != null) {
        await _firestore.collection('users').doc(currentUser!.uid).update({
          'chatHistory': [],
        });
      }
    } catch (e) {
      debugPrint('Error clearing chat history: $e');
      throw 'চ্যাট ইতিহাস মুছতে সমস্যা হয়েছে।';
    }
  }

  // Get user statistics
  Future<Map<String, int>> getUserStats() async {
    try {
      if (currentUser != null) {
        DocumentSnapshot doc = await _firestore.collection('users').doc(currentUser!.uid).get();
        Map<String, dynamic>? userData = doc.data() as Map<String, dynamic>?;

        // Initialize lifetime chat count for existing users if not present
        int totalLifetimeChats = userData?['totalLifetimeChats'] ?? 0;
        if (totalLifetimeChats == 0 && userData != null) {
          // For existing users, initialize with current chat history length
          List<dynamic> chatHistory = userData['chatHistory'] ?? [];
          totalLifetimeChats = chatHistory.length;

          // Update the user document with the initialized value
          await _firestore.collection('users').doc(currentUser!.uid).update({
            'totalLifetimeChats': totalLifetimeChats,
          });
        }

        // Get today's chat count - use consistent date format
        final now = DateTime.now();
        String today = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
        Map<String, dynamic> dailyChats = Map<String, dynamic>.from(userData?['dailyChats'] ?? {});
        int todayChats = dailyChats[today] ?? 0;

        // Calculate days since joining
        Timestamp? createdAt = userData?['createdAt'] as Timestamp?;
        int daysSinceJoining = 0;
        if (createdAt != null) {
          daysSinceJoining = DateTime.now().difference(createdAt.toDate()).inDays;
        }

        // Get total characters used
        int totalCharactersUsed = userData?['totalCharactersUsed'] ?? 0;

        return {
          'totalLifetimeChats': totalLifetimeChats, // Lifetime total (never decreases)
          'todayChats': todayChats, // Today's chat count
          'daysSinceJoining': daysSinceJoining,
          'profileCompletion': _calculateProfileCompletion(userData),
          'totalCharactersUsed': totalCharactersUsed, // Total characters used
        };
      }
      return {'totalLifetimeChats': 0, 'todayChats': 0, 'daysSinceJoining': 0, 'profileCompletion': 0, 'totalCharactersUsed': 0};
    } catch (e) {
      debugPrint('Error getting user stats: $e');
      return {'totalLifetimeChats': 0, 'todayChats': 0, 'daysSinceJoining': 0, 'profileCompletion': 0, 'totalCharactersUsed': 0};
    }
  }

  // Calculate profile completion percentage
  int _calculateProfileCompletion(Map<String, dynamic>? userData) {
    if (userData == null) return 0;

    int completedFields = 0;
    int totalFields = 4; // Only count user-editable fields: name, phone, address, profession

    // User-editable profile fields (25% each)
    if (userData['name'] != null && userData['name'].toString().isNotEmpty) completedFields++;
    if (userData['phone'] != null && userData['phone'].toString().isNotEmpty) completedFields++;
    if (userData['address'] != null && userData['address'].toString().isNotEmpty) completedFields++;
    if (userData['profession'] != null && userData['profession'].toString().isNotEmpty) completedFields++;

    return ((completedFields / totalFields) * 100).round();
  }

  // Test Firebase Storage connectivity
  Future<bool> testStorageConnectivity() async {
    try {
      debugPrint('Testing Firebase Storage connectivity...');
      final ref = _storage.ref().child('test').child('connectivity_test.txt');

      // Try to get metadata (this will fail if storage is not accessible)
      try {
        await ref.getMetadata();
        debugPrint('Storage connectivity test: SUCCESS - Storage is accessible');
        return true;
      } catch (e) {
        if (e.toString().contains('object-not-found')) {
          debugPrint('Storage connectivity test: SUCCESS - Storage is accessible (file not found is expected)');
          return true;
        } else {
          debugPrint('Storage connectivity test: FAILED - $e');
          return false;
        }
      }
    } catch (e) {
      debugPrint('Storage connectivity test: FAILED - $e');
      return false;
    }
  }

  // Upload profile picture with fallback to base64 storage
  Future<String> uploadProfilePicture(File imageFile) async {
    try {
      if (currentUser == null) {
        debugPrint('Upload failed: User not logged in');
        throw 'ব্যবহারকারী লগইন করেননি।';
      }

      debugPrint('Starting profile picture upload for user: ${currentUser!.uid}');
      debugPrint('Image file path: ${imageFile.path}');
      debugPrint('Image file exists: ${await imageFile.exists()}');
      debugPrint('Image file size: ${await imageFile.length()} bytes');

      // Test storage connectivity first
      final storageConnected = await testStorageConnectivity();
      if (!storageConnected) {
        throw 'Firebase Storage সংযোগ সমস্যা। ইন্টারনেট চেক করুন।';
      }

      // Check if file exists and is not empty
      if (!await imageFile.exists()) {
        debugPrint('Upload failed: Image file does not exist');
        throw 'ছবি ফাইল খুঁজে পাওয়া যায়নি।';
      }

      final fileSize = await imageFile.length();
      if (fileSize == 0) {
        debugPrint('Upload failed: Image file is empty');
        throw 'ছবি ফাইল খালি।';
      }

      // Create a reference to the profile pictures folder
      // Try different path approaches for better compatibility
      Reference ref;
      try {
        // First try: Standard path
        ref = _storage
            .ref()
            .child('profile_pictures')
            .child('${currentUser!.uid}.jpg');
        debugPrint('Firebase Storage reference created: ${ref.fullPath}');
      } catch (e) {
        debugPrint('Standard path failed, trying alternative: $e');
        // Alternative path
        ref = _storage.ref('profile_pictures/${currentUser!.uid}.jpg');
        debugPrint('Alternative Firebase Storage reference created: ${ref.fullPath}');
      }

      // Set metadata for the upload
      final metadata = SettableMetadata(
        contentType: 'image/jpeg',
        customMetadata: {
          'uploadedBy': currentUser!.uid,
          'uploadedAt': DateTime.now().toIso8601String(),
        },
      );

      // Upload the file with metadata
      debugPrint('Starting file upload...');

      // Try different upload approaches for better compatibility
      TaskSnapshot snapshot;
      try {
        // First try: Standard upload with metadata
        final uploadTask = ref.putFile(imageFile, metadata);

        // Monitor upload progress
        uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
          final progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          debugPrint('Upload progress: ${progress.toStringAsFixed(2)}%');
        });

        snapshot = await uploadTask;
      } catch (e) {
        debugPrint('Standard upload failed, trying simple upload: $e');

        // Second try: Simple upload without metadata
        final simpleUploadTask = ref.putFile(imageFile);
        snapshot = await simpleUploadTask;
      }
      debugPrint('Upload completed successfully');

      // Get the download URL
      debugPrint('Getting download URL...');
      final downloadUrl = await snapshot.ref.getDownloadURL();
      debugPrint('Download URL obtained: $downloadUrl');

      // Update user profile in Firebase Auth
      debugPrint('Updating Firebase Auth profile...');
      await currentUser!.updatePhotoURL(downloadUrl);
      debugPrint('Firebase Auth profile updated');

      // Update user document in Firestore
      debugPrint('Updating Firestore document...');
      await _firestore.collection('users').doc(currentUser!.uid).update({
        'photoURL': downloadUrl,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      debugPrint('Firestore document updated');

      debugPrint('Profile picture upload completed successfully');
      return downloadUrl;
    } on FirebaseException catch (e) {
      debugPrint('Firebase Storage error: ${e.code} - ${e.message}');

      // Try fallback method: store as base64 in Firestore
      if (e.code == 'storage/object-not-found' || e.code == 'storage/unauthorized') {
        debugPrint('Trying fallback method: storing image as base64 in Firestore');
        return await _uploadProfilePictureAsBase64(imageFile);
      }

      if (e.code == 'storage/unauthorized') {
        throw 'ছবি আপলোডের অনুমতি নেই। Firebase Storage rules আপডেট করুন।';
      } else if (e.code == 'storage/object-not-found') {
        throw 'Firebase Storage bucket সেটআপ সমস্যা। Admin এর সাথে যোগাযোগ করুন।';
      } else if (e.code == 'storage/canceled') {
        throw 'ছবি আপলোড বাতিল করা হয়েছে।';
      } else if (e.code == 'storage/unknown') {
        throw 'অজানা সমস্যা। ইন্টারনেট সংযোগ চেক করুন।';
      } else if (e.code == 'storage/retry-limit-exceeded') {
        throw 'আপলোড টাইমআউট। আবার চেষ্টা করুন।';
      } else if (e.code == 'storage/quota-exceeded') {
        throw 'Storage quota শেষ। Admin এর সাথে যোগাযোগ করুন।';
      } else {
        throw 'Firebase Storage সমস্যা: ${e.code} - ${e.message}';
      }
    } catch (e) {
      debugPrint('General error uploading profile picture: $e');
      // Try fallback method as last resort
      try {
        debugPrint('Trying fallback method due to general error');
        return await _uploadProfilePictureAsBase64(imageFile);
      } catch (fallbackError) {
        debugPrint('Fallback method also failed: $fallbackError');
        throw 'প্রোফাইল ছবি আপলোড করতে সমস্যা হয়েছে: $e';
      }
    }
  }

  // Fallback method: Upload profile picture as base64 to Firestore
  Future<String> _uploadProfilePictureAsBase64(File imageFile) async {
    try {
      debugPrint('Starting base64 upload fallback method');

      if (currentUser == null) {
        throw 'ব্যবহারকারী লগইন করেননি।';
      }

      // Read image file as bytes
      final bytes = await imageFile.readAsBytes();
      debugPrint('Image file size: ${bytes.length} bytes');

      // Check file size (limit to 1MB for Firestore)
      if (bytes.length > 1024 * 1024) {
        throw 'ছবির সাইজ খুব বড়। ১ MB এর কম সাইজের ছবি ব্যবহার করুন।';
      }

      // Convert to base64
      final base64String = base64Encode(bytes);
      debugPrint('Base64 conversion completed');

      // Create a data URL for the image
      final dataUrl = 'data:image/jpeg;base64,$base64String';

      // Update user document in Firestore with base64 image
      debugPrint('Updating Firestore with base64 image...');
      await _firestore.collection('users').doc(currentUser!.uid).update({
        'photoURL': dataUrl,
        'photoType': 'base64',
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update Firebase Auth profile
      debugPrint('Updating Firebase Auth profile...');
      await currentUser!.updatePhotoURL(dataUrl);

      debugPrint('Base64 profile picture upload completed successfully');
      return dataUrl;
    } catch (e) {
      debugPrint('Error in base64 upload: $e');
      throw 'ছবি আপলোড করতে সমস্যা হয়েছে (Fallback): $e';
    }
  }

  // Remove profile picture
  Future<void> removeProfilePicture() async {
    try {
      if (currentUser == null) {
        throw 'ব্যবহারকারী লগইন করেননি।';
      }

      // Delete from Firebase Storage
      try {
        final ref = _storage
            .ref()
            .child('profile_pictures')
            .child('${currentUser!.uid}.jpg');
        await ref.delete();
      } catch (e) {
        // File might not exist, continue with other operations
        debugPrint('Profile picture file not found in storage: $e');
      }

      // Update user profile in Firebase Auth
      await currentUser!.updatePhotoURL(null);

      // Update user document in Firestore
      await _firestore.collection('users').doc(currentUser!.uid).update({
        'photoURL': FieldValue.delete(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error removing profile picture: $e');
      throw 'প্রোফাইল ছবি মুছতে সমস্যা হয়েছে।';
    }
  }

  // Get profile picture URL
  Future<String?> getProfilePictureUrl() async {
    try {
      if (currentUser == null) return null;

      // First check Firebase Auth
      if (currentUser!.photoURL != null) {
        return currentUser!.photoURL;
      }

      // Then check Firestore
      final doc = await _firestore.collection('users').doc(currentUser!.uid).get();
      final userData = doc.data();
      return userData?['photoURL'];
    } catch (e) {
      debugPrint('Error getting profile picture URL: $e');
      return null;
    }
  }

  // Delete account
  Future<void> deleteAccount() async {
    try {
      if (currentUser != null) {
        // Delete profile picture from storage
        try {
          await removeProfilePicture();
        } catch (e) {
          // Continue even if profile picture deletion fails
          debugPrint('Error deleting profile picture during account deletion: $e');
        }

        // Delete user document from Firestore
        await _firestore.collection('users').doc(currentUser!.uid).delete();

        // Delete user account
        await currentUser!.delete();
      }
    } catch (e) {
      throw 'অ্যাকাউন্ট মুছতে সমস্যা হয়েছে।';
    }
  }
}
