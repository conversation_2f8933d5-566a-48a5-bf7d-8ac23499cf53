import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_theme.dart';
import 'dart:math' as math;

// Enhanced Glass Card with Professional Styling
class GlassCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final List<BoxShadow>? boxShadow;
  final Gradient? gradient;
  final Color? borderColor;
  final double borderWidth;
  final VoidCallback? onTap;

  const GlassCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.borderRadius,
    this.boxShadow,
    this.gradient,
    this.borderColor,
    this.borderWidth = 1.0,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      decoration: BoxDecoration(
        borderRadius: borderRadius ?? BorderRadius.circular(AppRadius.lg),
        gradient: gradient ?? LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.glassLight,
            AppColors.glassDark,
          ],
        ),
        border: Border.all(
          color: borderColor ?? AppColors.glassBorder,
          width: borderWidth,
        ),
        boxShadow: boxShadow ?? AppShadows.medium,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? BorderRadius.circular(AppRadius.lg),
          child: Padding(
            padding: padding ?? const EdgeInsets.all(AppSpacing.lg),
            child: child,
          ),
        ),
      ),
    );
  }
}

// Enhanced Animated Button with Multiple States
class AnimatedButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final bool isLoading;
  final bool isDisabled;
  final ButtonStyle? style;
  final Gradient? gradient;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;

  const AnimatedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.isLoading = false,
    this.isDisabled = false,
    this.style,
    this.gradient,
    this.width,
    this.height,
    this.padding,
  });

  @override
  State<AnimatedButton> createState() => _AnimatedButtonState();
}

class _AnimatedButtonState extends State<AnimatedButton>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _shimmerController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: AppAnimations.fast,
      vsync: this,
    );
    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: AppAnimations.easeInOut,
    ));

    _shimmerAnimation = Tween<double>(
      begin: -2.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _shimmerController,
      curve: Curves.linear,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _shimmerController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _scaleController.forward();
    HapticFeedback.lightImpact();
  }

  void _onTapUp(TapUpDetails details) {
    _scaleController.reverse();
  }

  void _onTapCancel() {
    _scaleController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final isEnabled = !widget.isDisabled && !widget.isLoading && widget.onPressed != null;

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: isEnabled ? _onTapDown : null,
            onTapUp: isEnabled ? _onTapUp : null,
            onTapCancel: isEnabled ? _onTapCancel : null,
            onTap: isEnabled ? widget.onPressed : null,
            child: Container(
              width: widget.width,
              height: widget.height ?? 50,
              padding: widget.padding ?? const EdgeInsets.symmetric(
                horizontal: AppSpacing.lg,
                vertical: AppSpacing.md,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppRadius.md),
                gradient: isEnabled
                    ? (widget.gradient ?? AppColors.primaryGradient)
                    : LinearGradient(
                        colors: [
                          AppColors.textMuted,
                          AppColors.textMuted.withOpacity(0.5),
                        ],
                      ),
                boxShadow: isEnabled ? AppShadows.glow : null,
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Shimmer effect
                  if (isEnabled && !widget.isLoading)
                    AnimatedBuilder(
                      animation: _shimmerAnimation,
                      builder: (context, child) {
                        return Positioned.fill(
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(AppRadius.md),
                            child: Transform.translate(
                              offset: Offset(_shimmerAnimation.value * 100, 0),
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      Colors.transparent,
                                      Colors.white.withOpacity(0.2),
                                      Colors.transparent,
                                    ],
                                    stops: const [0.0, 0.5, 1.0],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),

                  // Button content
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.isLoading)
                        const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      else if (widget.icon != null) ...[
                        Icon(
                          widget.icon,
                          color: Colors.white,
                          size: 20,
                        ),
                        const SizedBox(width: AppSpacing.sm),
                      ],

                      if (!widget.isLoading)
                        Text(
                          widget.text,
                          style: AppTextStyles.labelLarge.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// Enhanced Input Field with Professional Styling
class EnhancedTextField extends StatefulWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixIconTap;
  final bool obscureText;
  final TextInputType? keyboardType;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final bool enabled;
  final int maxLines;
  final EdgeInsetsGeometry? contentPadding;

  const EnhancedTextField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.obscureText = false,
    this.keyboardType,
    this.validator,
    this.onChanged,
    this.enabled = true,
    this.maxLines = 1,
    this.contentPadding,
  });

  @override
  State<EnhancedTextField> createState() => _EnhancedTextFieldState();
}

class _EnhancedTextFieldState extends State<EnhancedTextField>
    with TickerProviderStateMixin {
  late AnimationController _focusController;
  late Animation<double> _focusAnimation;
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusController = AnimationController(
      duration: AppAnimations.medium,
      vsync: this,
    );
    _focusAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _focusController,
      curve: AppAnimations.easeInOut,
    ));
    _focusNode = FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _focusController.dispose();
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
    if (_isFocused) {
      _focusController.forward();
    } else {
      _focusController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _focusAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppRadius.md),
            boxShadow: _isFocused ? AppShadows.glow : AppShadows.soft,
          ),
          child: TextFormField(
            controller: widget.controller,
            focusNode: _focusNode,
            obscureText: widget.obscureText,
            keyboardType: widget.keyboardType,
            validator: widget.validator,
            onChanged: widget.onChanged,
            enabled: widget.enabled,
            maxLines: widget.maxLines,
            style: AppTextStyles.bodyLarge,
            decoration: InputDecoration(
              labelText: widget.labelText,
              hintText: widget.hintText,
              labelStyle: AppTextStyles.labelMedium.copyWith(
                color: _isFocused
                    ? AppColors.primaryGreen
                    : AppColors.textSecondary,
              ),
              hintStyle: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textMuted,
              ),
              prefixIcon: widget.prefixIcon != null
                  ? Container(
                      margin: const EdgeInsets.all(AppSpacing.md),
                      padding: const EdgeInsets.all(AppSpacing.sm),
                      decoration: BoxDecoration(
                        color: _isFocused
                            ? AppColors.primaryGreen.withOpacity(0.1)
                            : AppColors.textMuted.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppRadius.sm),
                      ),
                      child: Icon(
                        widget.prefixIcon,
                        color: _isFocused
                            ? AppColors.primaryGreen
                            : AppColors.textMuted,
                        size: 20,
                      ),
                    )
                  : null,
              suffixIcon: widget.suffixIcon != null
                  ? IconButton(
                      onPressed: widget.onSuffixIconTap,
                      icon: Icon(
                        widget.suffixIcon,
                        color: _isFocused
                            ? AppColors.primaryGreen
                            : AppColors.textMuted,
                      ),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppRadius.md),
                borderSide: BorderSide(
                  color: AppColors.glassBorder,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppRadius.md),
                borderSide: BorderSide(
                  color: AppColors.glassBorder,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppRadius.md),
                borderSide: const BorderSide(
                  color: AppColors.primaryGreen,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppRadius.md),
                borderSide: const BorderSide(
                  color: AppColors.error,
                  width: 2,
                ),
              ),
              filled: true,
              fillColor: _isFocused
                  ? AppColors.glassLight
                  : AppColors.glassDark,
              contentPadding: widget.contentPadding ??
                  const EdgeInsets.symmetric(
                    horizontal: AppSpacing.lg,
                    vertical: AppSpacing.md,
                  ),
            ),
          ),
        );
      },
    );
  }
}

// Enhanced Animated Background with Professional Particles
class AnimatedBackground extends StatefulWidget {
  final Widget child;
  final int particleCount;
  final List<Color> particleColors;
  final double particleSpeed;

  const AnimatedBackground({
    super.key,
    required this.child,
    this.particleCount = 25,
    this.particleColors = const [
      AppColors.primaryGreen,
      AppColors.primaryPurple,
      AppColors.accentPink,
      AppColors.accentBlue,
      AppColors.accentCyan,
    ],
    this.particleSpeed = 1.0,
  });

  @override
  State<AnimatedBackground> createState() => _AnimatedBackgroundState();
}

class _AnimatedBackgroundState extends State<AnimatedBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<Particle> _particles;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _particles = List.generate(widget.particleCount, (index) {
      return Particle(
        color: widget.particleColors[index % widget.particleColors.length],
        speed: widget.particleSpeed,
        index: index,
      );
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Background gradient
        Container(
          decoration: const BoxDecoration(
            gradient: AppColors.backgroundGradient,
          ),
        ),

        // Animated particles
        AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return CustomPaint(
              painter: ParticlePainter(
                particles: _particles,
                animationValue: _controller.value,
                size: MediaQuery.of(context).size,
              ),
              size: Size.infinite,
            );
          },
        ),

        // Floating geometric shapes
        AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return Stack(
              children: List.generate(8, (index) {
                final size = MediaQuery.of(context).size;
                final offset = Offset(
                  (index * 120.0 + _controller.value * 100) % size.width,
                  (index * 100.0 + math.sin(_controller.value * 2 * math.pi + index) * 50) % size.height,
                );

                return Positioned(
                  left: offset.dx,
                  top: offset.dy,
                  child: Transform.rotate(
                    angle: _controller.value * 2 * math.pi + index,
                    child: Container(
                      width: 30 + (index % 3) * 15,
                      height: 30 + (index % 3) * 15,
                      decoration: BoxDecoration(
                        shape: index % 2 == 0 ? BoxShape.circle : BoxShape.rectangle,
                        borderRadius: index % 2 == 1 ? BorderRadius.circular(8) : null,
                        border: Border.all(
                          color: widget.particleColors[index % widget.particleColors.length]
                              .withOpacity(0.3),
                          width: 2,
                        ),
                        gradient: LinearGradient(
                          colors: [
                            widget.particleColors[index % widget.particleColors.length]
                                .withOpacity(0.1),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              }),
            );
          },
        ),

        // Main content
        widget.child,
      ],
    );
  }
}

// Particle class for background animation
class Particle {
  final Color color;
  final double speed;
  final int index;
  late double x;
  late double y;
  late double size;
  late double opacity;

  Particle({
    required this.color,
    required this.speed,
    required this.index,
  }) {
    x = (index * 80.0) % 400;
    y = (index * 60.0) % 800;
    size = 2 + (index % 4) * 1.5;
    opacity = 0.3 + (index % 3) * 0.2;
  }
}

// Custom painter for particles
class ParticlePainter extends CustomPainter {
  final List<Particle> particles;
  final double animationValue;
  final Size size;

  ParticlePainter({
    required this.particles,
    required this.animationValue,
    required this.size,
  });

  @override
  void paint(Canvas canvas, Size size) {
    for (int i = 0; i < particles.length; i++) {
      final particle = particles[i];
      final paint = Paint()
        ..color = particle.color.withOpacity(particle.opacity)
        ..style = PaintingStyle.fill;

      final x = (particle.x + animationValue * 200 * particle.speed) % size.width;
      final y = (particle.y + animationValue * 150 * particle.speed) % size.height;

      // Draw particle with glow effect
      canvas.drawCircle(
        Offset(x, y),
        particle.size,
        paint,
      );

      // Add glow effect
      final glowPaint = Paint()
        ..color = particle.color.withOpacity(particle.opacity * 0.3)
        ..style = PaintingStyle.fill
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

      canvas.drawCircle(
        Offset(x, y),
        particle.size * 2,
        glowPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Enhanced Loading Animation
class EnhancedLoadingAnimation extends StatefulWidget {
  final String? message;
  final Color? color;
  final double size;

  const EnhancedLoadingAnimation({
    super.key,
    this.message,
    this.color,
    this.size = 60,
  });

  @override
  State<EnhancedLoadingAnimation> createState() => _EnhancedLoadingAnimationState();
}

class _EnhancedLoadingAnimationState extends State<EnhancedLoadingAnimation>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late AnimationController _particleController;
  late AnimationController _morphController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _waveAnimation;
  late Animation<double> _particleAnimation;
  late Animation<double> _morphAnimation;

  @override
  void initState() {
    super.initState();

    // Multiple animation controllers for complex effects
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    )..repeat();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat(reverse: true);

    _waveController = AnimationController(
      duration: const Duration(milliseconds: 4000),
      vsync: this,
    )..repeat();

    _particleController = AnimationController(
      duration: const Duration(milliseconds: 5000),
      vsync: this,
    )..repeat();

    _morphController = AnimationController(
      duration: const Duration(milliseconds: 6000),
      vsync: this,
    )..repeat();

    // Create smooth animations
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.elasticInOut,
    ));

    _waveAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _waveController,
      curve: Curves.easeInOut,
    ));

    _particleAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _particleController,
      curve: Curves.linear,
    ));

    _morphAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _morphController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    _waveController.dispose();
    _particleController.dispose();
    _morphController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Stunning multi-layered loading animation with perfect visualization
        AnimatedBuilder(
          animation: Listenable.merge([
            _rotationAnimation,
            _pulseAnimation,
            _waveAnimation,
            _particleAnimation,
            _morphAnimation
          ]),
          builder: (context, child) {
            return Container(
              width: widget.size * 1.8,
              height: widget.size * 1.8,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.size),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primaryGreen.withOpacity(0.3),
                    blurRadius: 30,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Stack(
                alignment: Alignment.center,
                clipBehavior: Clip.none,
                children: [
                  // Background glow effect
                  _buildBackgroundGlow(),

                  // Outer rotating ring with particles
                  _buildOuterParticleRing(),

                  // Middle wave ripples
                  _buildWaveRipples(),

                  // Inner rotating rings
                  _buildInnerRotatingRings(),

                  // Central morphing core
                  _buildCentralCore(),

                  // Top floating sparkles
                  _buildFloatingSparkles(),
                ],
              ),
            );
          },
        ),
        if (widget.message != null) ...[
          const SizedBox(height: 20),
          // Enhanced animated text
          AnimatedBuilder(
            animation: _waveAnimation,
            builder: (context, child) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primaryGreen.withOpacity(0.1),
                      AppColors.primaryPurple.withOpacity(0.1),
                    ],
                  ),
                ),
                child: ShaderMask(
                  shaderCallback: (bounds) => LinearGradient(
                    colors: const [
                      AppColors.primaryGreen,
                      AppColors.primaryPurple,
                      AppColors.primaryGreen,
                    ],
                    stops: [
                      (_waveAnimation.value - 0.3).clamp(0.0, 1.0),
                      _waveAnimation.value,
                      (_waveAnimation.value + 0.3).clamp(0.0, 1.0),
                    ],
                  ).createShader(bounds),
                  child: Text(
                    widget.message!,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      letterSpacing: 0.5,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              );
            },
          ),
        ],
      ],
    );
  }

  // Build background glow effect
  Widget _buildBackgroundGlow() {
    return Container(
      width: widget.size * 1.6,
      height: widget.size * 1.6,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            AppColors.primaryGreen.withOpacity(0.2 * _pulseAnimation.value),
            AppColors.primaryPurple.withOpacity(0.1 * _pulseAnimation.value),
            Colors.transparent,
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
    );
  }

  // Build outer particle ring
  Widget _buildOuterParticleRing() {
    return CustomPaint(
      size: Size(widget.size * 1.4, widget.size * 1.4),
      painter: ParticleRingPainter(
        progress: _particleAnimation.value,
        rotation: _rotationAnimation.value,
        pulse: _pulseAnimation.value,
      ),
    );
  }

  // Build wave ripple effects
  Widget _buildWaveRipples() {
    return Stack(
      alignment: Alignment.center,
      children: List.generate(3, (index) {
        final delay = index * 0.3;
        final waveProgress = (_waveAnimation.value + delay) % 1.0;
        return Transform.scale(
          scale: 0.5 + (waveProgress * 1.5),
          child: Opacity(
            opacity: (1.0 - waveProgress) * 0.6,
            child: Container(
              width: widget.size * 1.5,
              height: widget.size * 1.5,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppColors.primaryGreen.withOpacity(0.4),
                  width: 2,
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  // Build inner rotating rings
  Widget _buildInnerRotatingRings() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Outer ring
        Transform.rotate(
          angle: _rotationAnimation.value,
          child: Container(
            width: widget.size * 1.2,
            height: widget.size * 1.2,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: SweepGradient(
                colors: const [
                  Colors.transparent,
                  AppColors.primaryGreen,
                  Colors.transparent,
                  AppColors.primaryPurple,
                  Colors.transparent,
                ],
                stops: const [0.0, 0.2, 0.4, 0.6, 1.0],
              ),
            ),
          ),
        ),
        // Inner ring (counter-rotating)
        Transform.rotate(
          angle: -_rotationAnimation.value * 1.5,
          child: Container(
            width: widget.size * 0.9,
            height: widget.size * 0.9,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: SweepGradient(
                colors: const [
                  Colors.transparent,
                  AppColors.primaryPurple,
                  Colors.transparent,
                  Color(0xFFff3e9d),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Build central core
  Widget _buildCentralCore() {
    return Transform.scale(
      scale: _pulseAnimation.value * 0.8,
      child: Container(
        width: widget.size * 0.6,
        height: widget.size * 0.6,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: RadialGradient(
            colors: [
              Colors.white.withOpacity(0.9),
              AppColors.primaryGreen.withOpacity(0.7),
              AppColors.primaryPurple.withOpacity(0.5),
              Colors.transparent,
            ],
            stops: const [0.0, 0.3, 0.7, 1.0],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.white.withOpacity(0.6),
              blurRadius: 20,
              spreadRadius: 3,
            ),
            BoxShadow(
              color: AppColors.primaryGreen.withOpacity(0.4),
              blurRadius: 40,
              spreadRadius: 8,
            ),
          ],
        ),
        child: Center(
          child: Transform.rotate(
            angle: _rotationAnimation.value * 2,
            child: Icon(
              Icons.auto_awesome,
              color: Colors.white.withOpacity(0.9),
              size: widget.size * 0.25,
            ),
          ),
        ),
      ),
    );
  }

  // Build floating sparkles
  Widget _buildFloatingSparkles() {
    return CustomPaint(
      size: Size(widget.size * 1.6, widget.size * 1.6),
      painter: FloatingParticlesPainter(
        progress: _particleAnimation.value,
        wave: _waveAnimation.value,
        pulse: _pulseAnimation.value,
      ),
    );
  }
}

// Enhanced Profile Avatar with Professional Styling
class EnhancedProfileAvatar extends StatefulWidget {
  final String? imageUrl;
  final String userName;
  final double size;
  final VoidCallback? onTap;
  final bool showEditButton;
  final bool isLoading;

  const EnhancedProfileAvatar({
    super.key,
    this.imageUrl,
    required this.userName,
    this.size = 100,
    this.onTap,
    this.showEditButton = false,
    this.isLoading = false,
  });

  @override
  State<EnhancedProfileAvatar> createState() => _EnhancedProfileAvatarState();
}

class _EnhancedProfileAvatarState extends State<EnhancedProfileAvatar>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: GestureDetector(
            onTap: widget.onTap,
            child: Stack(
              children: [
                Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: AppColors.primaryGradient,
                    boxShadow: AppShadows.glow,
                  ),
                  child: widget.isLoading
                      ? const Center(
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 3,
                          ),
                        )
                      : widget.imageUrl != null
                          ? ClipOval(
                              child: Image.network(
                                widget.imageUrl!,
                                width: widget.size,
                                height: widget.size,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return _buildDefaultAvatar();
                                },
                              ),
                            )
                          : _buildDefaultAvatar(),
                ),

                if (widget.showEditButton && !widget.isLoading)
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: widget.size * 0.3,
                      height: widget.size * 0.3,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.primaryGreen,
                        border: Border.all(
                          color: AppColors.darkBg,
                          width: 2,
                        ),
                        boxShadow: AppShadows.medium,
                      ),
                      child: Icon(
                        Icons.camera_alt,
                        color: Colors.white,
                        size: widget.size * 0.15,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDefaultAvatar() {
    return Center(
      child: Text(
        widget.userName.isNotEmpty ? widget.userName[0].toUpperCase() : 'U',
        style: AppTextStyles.displayMedium.copyWith(
          color: Colors.white,
          fontSize: widget.size * 0.4,
        ),
      ),
    );
  }
}

// Custom painter for mesmerizing particle ring
class ParticleRingPainter extends CustomPainter {
  final double progress;
  final double rotation;
  final double pulse;

  ParticleRingPainter({
    required this.progress,
    required this.rotation,
    required this.pulse,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2.5;
    final paint = Paint()..style = PaintingStyle.fill;

    // Create particle ring with 8 particles for better performance
    for (int i = 0; i < 8; i++) {
      final angle = (i / 8) * 2 * math.pi + rotation;
      final particleRadius = 2.5 + (pulse * 1.5);

      // Calculate particle position with slight orbit variation
      final orbitRadius = radius + (math.sin(progress * 3 * math.pi + i) * 8);
      final x = center.dx + math.cos(angle) * orbitRadius;
      final y = center.dy + math.sin(angle) * orbitRadius;

      // Create gradient effect for each particle
      final gradient = RadialGradient(
        colors: [
          _getParticleColor(i).withOpacity(0.8),
          _getParticleColor(i).withOpacity(0.4),
          Colors.transparent,
        ],
        stops: const [0.0, 0.6, 1.0],
      );

      paint.shader = gradient.createShader(
        Rect.fromCircle(center: Offset(x, y), radius: particleRadius * 1.8),
      );

      canvas.drawCircle(Offset(x, y), particleRadius, paint);

      // Add simplified trailing effect
      const trailLength = 3;
      for (int j = 1; j <= trailLength; j++) {
        final trailAngle = angle - (j * 0.15);
        final trailX = center.dx + math.cos(trailAngle) * orbitRadius;
        final trailY = center.dy + math.sin(trailAngle) * orbitRadius;
        final trailOpacity = (1.0 - (j / trailLength)) * 0.4;

        paint.color = _getParticleColor(i).withOpacity(trailOpacity);
        canvas.drawCircle(
          Offset(trailX, trailY),
          particleRadius * (1.0 - (j / trailLength * 0.5)),
          paint,
        );
      }
    }
  }

  Color _getParticleColor(int index) {
    final colors = [
      AppColors.primaryGreen,
      AppColors.primaryPurple,
      const Color(0xFFff3e9d),
      const Color(0xFF0e8aff),
      const Color(0xFFffd700),
      const Color(0xFF00ff88),
    ];
    return colors[index % colors.length];
  }

  @override
  bool shouldRepaint(ParticleRingPainter oldDelegate) {
    return oldDelegate.progress != progress ||
           oldDelegate.rotation != rotation ||
           oldDelegate.pulse != pulse;
  }
}

// Custom painter for floating particles
class FloatingParticlesPainter extends CustomPainter {
  final double progress;
  final double wave;
  final double pulse;

  FloatingParticlesPainter({
    required this.progress,
    required this.wave,
    required this.pulse,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final paint = Paint()..style = PaintingStyle.fill;

    // Create floating particles with optimized count
    for (int i = 0; i < 12; i++) {
      final baseAngle = (i / 12) * 2 * math.pi;
      final radius = (size.width / 3) + (i % 2) * 15;

      // Simplified movement pattern
      final waveOffset = math.sin(wave * 2 * math.pi + i) * 12;
      final progressOffset = progress * 1.5 * math.pi;
      final angle = baseAngle + progressOffset + (waveOffset * 0.08);

      final x = center.dx + math.cos(angle) * (radius + waveOffset);
      final y = center.dy + math.sin(angle) * (radius + waveOffset) +
                (math.sin(progress * 2.5 * math.pi + i) * 8);

      // Optimized particle size
      final particleSize = (1.5 + (i % 2)) * (0.9 + pulse * 0.3) *
                         (0.8 + 0.2 * math.sin(progress * 3 * math.pi + i));

      // Simplified color cycling
      final colorIndex = (progress * 4 + i) % 6;
      final color = _getFloatingParticleColor(colorIndex.floor());

      // Optimized opacity calculation
      final distanceFromCenter = math.sqrt(
        math.pow(x - center.dx, 2) + math.pow(y - center.dy, 2)
      );
      final maxDistance = size.width / 2;
      final opacity = (1.0 - (distanceFromCenter / maxDistance)).clamp(0.0, 0.7);

      paint.color = color.withOpacity(opacity);
      canvas.drawCircle(Offset(x, y), particleSize, paint);

      // Simplified glow effect
      paint.color = color.withOpacity(opacity * 0.25);
      canvas.drawCircle(Offset(x, y), particleSize * 1.8, paint);
    }
  }

  Color _getFloatingParticleColor(int index) {
    final colors = [
      AppColors.primaryGreen,
      AppColors.primaryPurple,
      const Color(0xFFff3e9d),
      const Color(0xFF0e8aff),
      const Color(0xFFffd700),
      Colors.white,
    ];
    return colors[index % colors.length];
  }

  @override
  bool shouldRepaint(FloatingParticlesPainter oldDelegate) {
    return oldDelegate.progress != progress ||
           oldDelegate.wave != wave ||
           oldDelegate.pulse != pulse;
  }
}