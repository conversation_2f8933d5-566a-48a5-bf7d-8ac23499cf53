@echo off
echo ========================================
echo UkilGiri App - Complete APK Build Script
echo ========================================
echo.

echo [1/6] Cleaning previous builds...
call flutter clean
if %errorlevel% neq 0 (
    echo ERROR: Flutter clean failed!
    pause
    exit /b 1
)

echo.
echo [2/6] Getting dependencies...
call flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Flutter pub get failed!
    pause
    exit /b 1
)

echo.
echo [3/6] Analyzing code for issues...
call flutter analyze
if %errorlevel% neq 0 (
    echo WARNING: Code analysis found issues, but continuing...
)

echo.
echo [4/6] Running tests...
call flutter test
if %errorlevel% neq 0 (
    echo WARNING: Tests failed, but continuing with build...
)

echo.
echo [5/6] Building release APK with all optimizations...
call flutter build apk --release --shrink --obfuscate --split-debug-info=build/debug-info
if %errorlevel% neq 0 (
    echo ERROR: APK build failed!
    pause
    exit /b 1
)

echo.
echo [6/6] Build completed successfully!
echo.
echo ========================================
echo APK LOCATION:
echo build\app\outputs\flutter-apk\app-release.apk
echo ========================================
echo.
echo APK Features Included:
echo ✅ Firebase Authentication (Gmail Sign-in)
echo ✅ AI Chat with Groq API
echo ✅ Chat History Management
echo ✅ Profile Management with Image Upload
echo ✅ Find Ukil Feature
echo ✅ Enhanced Scrollbar
echo ✅ Professional UI/UX
echo ✅ Bengali Text Support
echo ✅ Optimized Performance
echo ✅ Reduced APK Size
echo.
echo Ready for installation on Android devices!
echo.
pause
