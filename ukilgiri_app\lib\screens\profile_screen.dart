import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';
import '../services/auth_service.dart';
import '../theme/app_theme.dart';
import '../widgets/enhanced_components.dart';
import 'auth_screen.dart';
import 'dart:math' as math;

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen>
    with TickerProviderStateMixin {
  final AuthService _authService = AuthService();
  late AnimationController _pulseController;
  late AnimationController _rotationController;

  Map<String, dynamic>? _userData;
  bool _isLoading = true;
  bool _isEditing = false;

  // Form controllers
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _professionController = TextEditingController();

  // Validation state
  Map<String, String?> _validationErrors = {};
  bool _isFormValid = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadUserData();
    _initializePermissions();
    _setupFormListeners();
  }

  // Initialize permissions in background
  Future<void> _initializePermissions() async {
    try {
      // Check permissions status without requesting
      await Permission.camera.status;
      await Permission.storage.status;
      await Permission.photos.status;
    } catch (e) {
      debugPrint('Permission initialization error: $e');
    }
  }

  // Show in-app permission request dialog
  Future<bool> _showPermissionDialog({
    required String title,
    required String message,
    required IconData icon,
  }) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppColors.darkBg,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: AppColors.primaryGreen.withOpacity(0.3),
            ),
          ),
          title: Row(
            children: [
              Icon(
                icon,
                color: AppColors.primaryGreen,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
            ],
          ),
          content: Text(
            message,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textPrimary.withOpacity(0.8),
              height: 1.5,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'বাতিল',
                style: TextStyle(
                  color: AppColors.textPrimary.withOpacity(0.6),
                  fontSize: 14,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGreen,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              ),
              child: const Text(
                'অনুমতি দিন',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    ) ?? false;
  }

  // Show settings dialog for permanently denied permissions
  Future<bool> _showSettingsDialog({
    required String title,
    required String message,
  }) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppColors.darkBg,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: AppColors.primaryGreen.withOpacity(0.3),
            ),
          ),
          title: Row(
            children: [
              Icon(
                Icons.settings,
                color: AppColors.primaryGreen,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
            ],
          ),
          content: Text(
            message,
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textPrimary.withOpacity(0.8),
              height: 1.5,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'বাতিল',
                style: TextStyle(
                  color: AppColors.textPrimary.withOpacity(0.6),
                  fontSize: 14,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryGreen,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              ),
              child: const Text(
                'সেটিংস খুলুন',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    ) ?? false;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh user stats when the screen becomes visible
    if (!_isLoading) {
      _loadUserData();
    }
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);

    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat();
  }

  void _setupFormListeners() {
    _nameController.addListener(_validateForm);
    _phoneController.addListener(_validateForm);
    _addressController.addListener(_validateForm);
    _professionController.addListener(_validateForm);
  }

  void _validateForm() {
    if (!_isEditing) return;

    Map<String, String?> errors = {};

    // Validate name
    String name = _nameController.text.trim();
    if (name.isEmpty) {
      errors['name'] = 'নাম লিখুন';
    } else if (name.length < 2) {
      errors['name'] = 'নাম কমপক্ষে ২টি অক্ষর হতে হবে';
    } else if (!RegExp(r'^[a-zA-Z\u0980-\u09FF\s]+$').hasMatch(name)) {
      errors['name'] = 'নামে শুধুমাত্র অক্ষর ব্যবহার করুন';
    }

    // Validate phone
    String phone = _phoneController.text.trim();
    if (phone.isEmpty) {
      errors['phone'] = 'ফোন নম্বর লিখুন';
    } else if (!RegExp(r'^(\+88)?01[3-9]\d{8}$').hasMatch(phone)) {
      errors['phone'] = 'সঠিক বাংলাদেশী ফোন নম্বর লিখুন (01XXXXXXXXX)';
    }

    // Validate address
    String address = _addressController.text.trim();
    if (address.isEmpty) {
      errors['address'] = 'ঠিকানা লিখুন';
    } else if (address.length < 5) {
      errors['address'] = 'ঠিকানা কমপক্ষে ৫টি অক্ষর হতে হবে';
    }

    // Validate profession
    String profession = _professionController.text.trim();
    if (profession.isEmpty) {
      errors['profession'] = 'পেশা লিখুন';
    } else if (profession.length < 2) {
      errors['profession'] = 'পেশা কমপক্ষে ২টি অক্ষর হতে হবে';
    }

    bool isValid = errors.isEmpty;

    setState(() {
      _validationErrors = errors;
      _isFormValid = isValid;
    });
  }

  Future<void> _loadUserData() async {
    try {
      final userData = await _authService.getUserData();
      if (userData != null) {
        setState(() {
          _userData = userData;
          _nameController.text = userData['name'] ?? '';
          _phoneController.text = userData['phone'] ?? '';
          _addressController.text = userData['address'] ?? '';
          _professionController.text = userData['profession'] ?? '';
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('ব্যবহারকারীর তথ্য লোড করতে সমস্যা হয়েছে');
    }
  }

  Future<void> _updateUserData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      await _authService.updateUserProfile(
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        address: _addressController.text.trim(),
        profession: _professionController.text.trim(),
      );

      await _loadUserData();
      setState(() {
        _isEditing = false;
        _isLoading = false;
      });

      _showSuccessSnackBar('প্রোফাইল সফলভাবে আপডেট হয়েছে');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      debugPrint('Profile update error: $e');

      // Show more specific error message
      String errorMessage = 'প্রোফাইল আপডেট করতে সমস্যা হয়েছে';
      if (e.toString().contains('network')) {
        errorMessage = 'ইন্টারনেট সংযোগ পরীক্ষা করুন';
      } else if (e.toString().contains('permission')) {
        errorMessage = 'অনুমতির সমস্যা হয়েছে';
      }

      _showErrorSnackBar(errorMessage);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.primaryGreen,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // Show profile picture options
  void _showProfilePictureOptions() {
    debugPrint('_showProfilePictureOptions called');
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.darkBg,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          border: Border.all(
            color: AppColors.primaryGreen.withOpacity(0.2),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.textPrimary.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'প্রোফাইল ছবি আপডেট করুন',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildPictureOption(
                  icon: Icons.camera_alt,
                  label: 'ক্যামেরা',
                  onTap: () {
                    debugPrint('Camera option tapped');
                    Navigator.pop(context);
                    _pickImageFromCamera();
                  },
                ),
                _buildPictureOption(
                  icon: Icons.photo_library,
                  label: 'গ্যালারি',
                  onTap: () {
                    debugPrint('Gallery option tapped');
                    Navigator.pop(context);
                    _pickImageFromGallery();
                  },
                ),
                _buildPictureOption(
                  icon: Icons.delete,
                  label: 'মুছুন',
                  color: Colors.red,
                  onTap: () {
                    Navigator.pop(context);
                    _removeProfilePicture();
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Add restart app option for channel errors
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: TextButton.icon(
                onPressed: () {
                  Navigator.pop(context);
                  _showRestartDialog();
                },
                icon: const Icon(Icons.refresh, color: AppColors.primaryPurple, size: 18),
                label: const Text(
                  'ছবি নির্বাচনে সমস্যা হলে অ্যাপ পুনরায় চালু করুন',
                  style: TextStyle(
                    color: AppColors.primaryPurple,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                style: TextButton.styleFrom(
                  backgroundColor: AppColors.primaryPurple.withOpacity(0.1),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(
                      color: AppColors.primaryPurple.withOpacity(0.3),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // Show restart dialog
  void _showRestartDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.darkBg,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: AppColors.primaryGreen.withOpacity(0.3),
          ),
        ),
        title: const Row(
          children: [
            Icon(
              Icons.refresh,
              color: AppColors.primaryPurple,
              size: 24,
            ),
            SizedBox(width: 12),
            Text(
              'অ্যাপ পুনরায় চালু করুন',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
        content: const Text(
          'ছবি নির্বাচনে সমস্যা হলে অ্যাপটি বন্ধ করে আবার খুলুন। এটি সাধারণত চ্যানেল কমিউনিকেশন সমস্যার সমাধান করে।',
          style: TextStyle(
            fontSize: 14,
            color: AppColors.textPrimary,
            height: 1.5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'বাতিল',
              style: TextStyle(
                color: AppColors.textPrimary.withOpacity(0.6),
                fontSize: 14,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Close the app (user will need to manually restart)
              SystemNavigator.pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryPurple,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'অ্যাপ বন্ধ করুন',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPictureOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    Color? color,
  }) {
    final optionColor = color ?? AppColors.primaryGreen;
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: optionColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: optionColor.withOpacity(0.3),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: optionColor,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: optionColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Request camera permission with in-app dialog
  Future<bool> _requestCameraPermission() async {
    try {
      var status = await Permission.camera.status;

      if (status.isGranted) {
        return true;
      }

      if (status.isDenied) {
        // Show in-app permission dialog
        bool shouldRequest = await _showPermissionDialog(
          title: 'ক্যামেরা অনুমতি প্রয়োজন',
          message: 'প্রোফাইল ছবি তুলতে ক্যামেরা ব্যবহার করার জন্য অনুমতি দিন।',
          icon: Icons.camera_alt,
        );

        if (shouldRequest) {
          status = await Permission.camera.request();
          return status.isGranted;
        }
        return false;
      }

      if (status.isPermanentlyDenied) {
        // Show settings dialog
        bool shouldOpenSettings = await _showSettingsDialog(
          title: 'ক্যামেরা অনুমতি প্রয়োজন',
          message: 'ক্যামেরা ব্যবহার করতে সেটিংস থেকে অনুমতি দিন।',
        );

        if (shouldOpenSettings) {
          await openAppSettings();
        }
        return false;
      }

      return false;
    } catch (e) {
      debugPrint('Camera permission error: $e');
      return false;
    }
  }

  // Request storage permission with in-app dialog
  Future<bool> _requestStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        // Check current permissions
        var storageStatus = await Permission.storage.status;
        var photosStatus = await Permission.photos.status;

        // If any permission is already granted, return true
        if (storageStatus.isGranted || photosStatus.isGranted) {
          return true;
        }

        // If permissions are denied, show dialog and request
        if (storageStatus.isDenied || photosStatus.isDenied) {
          bool shouldRequest = await _showPermissionDialog(
            title: 'স্টোরেজ অনুমতি প্রয়োজন',
            message: 'গ্যালারি থেকে ছবি নির্বাচন করতে স্টোরেজ অ্যাক্সেসের অনুমতি দিন।',
            icon: Icons.photo_library,
          );

          if (shouldRequest) {
            // Request both permissions
            var newStorageStatus = await Permission.storage.request();
            var newPhotosStatus = await Permission.photos.request();

            return newStorageStatus.isGranted || newPhotosStatus.isGranted;
          }
          return false;
        }

        // If permanently denied, show settings dialog
        if (storageStatus.isPermanentlyDenied || photosStatus.isPermanentlyDenied) {
          bool shouldOpenSettings = await _showSettingsDialog(
            title: 'স্টোরেজ অনুমতি প্রয়োজন',
            message: 'গ্যালারি ব্যবহার করতে সেটিংস থেকে অনুমতি দিন।',
          );

          if (shouldOpenSettings) {
            await openAppSettings();
          }
          return false;
        }
      }
      return true;
    } catch (e) {
      debugPrint('Storage permission error: $e');
      return false;
    }
  }

  // Pick image from camera with in-app permission handling
  Future<void> _pickImageFromCamera() async {
    debugPrint('_pickImageFromCamera called');
    try {
      debugPrint('Opening camera directly...');

      // Create a new instance of ImagePicker for each call
      final ImagePicker picker = ImagePicker();

      // Add a small delay to ensure proper initialization
      await Future.delayed(const Duration(milliseconds: 100));

      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
        preferredCameraDevice: CameraDevice.rear,
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          debugPrint('Camera timeout');
          return null;
        },
      );

      debugPrint('Camera result: ${image?.path}');
      if (image != null) {
        debugPrint('Uploading image...');
        await _uploadProfilePicture(File(image.path));
      } else {
        debugPrint('No image selected from camera');
        _showErrorSnackBar('কোনো ছবি নির্বাচন করা হয়নি');
      }
    } on PlatformException catch (e) {
      debugPrint('Platform error: ${e.code} - ${e.message}');
      if (e.code == 'channel-error') {
        _showErrorSnackBar('ক্যামেরা সেবা উপলব্ধ নেই। অ্যাপ পুনরায় চালু করুন।');
      } else {
        _showErrorSnackBar('ক্যামেরা খুলতে সমস্যা হয়েছে: ${e.message}');
      }
    } catch (e) {
      debugPrint('Camera error: $e');
      _showErrorSnackBar('ক্যামেরা থেকে ছবি নিতে সমস্যা হয়েছে');
    }
  }

  // Pick image from gallery with in-app permission handling
  Future<void> _pickImageFromGallery() async {
    debugPrint('_pickImageFromGallery called');
    try {
      debugPrint('Opening gallery directly...');

      // Create a new instance of ImagePicker for each call
      final ImagePicker picker = ImagePicker();

      // Add a small delay to ensure proper initialization
      await Future.delayed(const Duration(milliseconds: 100));

      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          debugPrint('Gallery timeout');
          return null;
        },
      );

      debugPrint('Gallery result: ${image?.path}');
      if (image != null) {
        debugPrint('Uploading image...');
        await _uploadProfilePicture(File(image.path));
      } else {
        debugPrint('No image selected from gallery');
        _showErrorSnackBar('কোনো ছবি নির্বাচন করা হয়নি');
      }
    } on PlatformException catch (e) {
      debugPrint('Platform error: ${e.code} - ${e.message}');
      if (e.code == 'channel-error') {
        _showErrorSnackBar('গ্যালারি সেবা উপলব্ধ নেই। অ্যাপ পুনরায় চালু করুন।');
      } else {
        _showErrorSnackBar('গ্যালারি খুলতে সমস্যা হয়েছে: ${e.message}');
      }
    } catch (e) {
      debugPrint('Gallery error: $e');
      _showErrorSnackBar('গ্যালারি থেকে ছবি নিতে সমস্যা হয়েছে');
    }
  }

  // Upload profile picture
  Future<void> _uploadProfilePicture(File imageFile) async {
    try {
      setState(() {
        _isLoading = true;
      });

      debugPrint('Profile screen: Starting upload for file: ${imageFile.path}');
      await _authService.uploadProfilePicture(imageFile);
      await _loadUserData();

      _showSuccessSnackBar('প্রোফাইল ছবি সফলভাবে আপডেট হয়েছে');
      debugPrint('Profile screen: Upload completed successfully');
    } catch (e) {
      debugPrint('Profile screen: Upload failed with error: $e');
      _showErrorSnackBar('প্রোফাইল ছবি আপলোড করতে সমস্যা: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Remove profile picture
  Future<void> _removeProfilePicture() async {
    try {
      setState(() {
        _isLoading = true;
      });

      await _authService.removeProfilePicture();
      await _loadUserData();

      _showSuccessSnackBar('প্রোফাইল ছবি সফলভাবে মুছে ফেলা হয়েছে');
    } catch (e) {
      _showErrorSnackBar('প্রোফাইল ছবি মুছতে সমস্যা হয়েছে');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _signOut() async {
    try {
      await _authService.signOut();
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const AuthScreen()),
          (route) => false,
        );
      }
    } catch (e) {
      _showErrorSnackBar('সাইন আউট করতে সমস্যা হয়েছে');
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();

    // Remove listeners before disposing controllers
    _nameController.removeListener(_validateForm);
    _phoneController.removeListener(_validateForm);
    _addressController.removeListener(_validateForm);
    _professionController.removeListener(_validateForm);

    _nameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _professionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.darkBg,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF0a0a0a),
              AppColors.primaryGreen.withOpacity(0.05),
              AppColors.primaryPurple.withOpacity(0.05),
              const Color(0xFF1a1a1a),
              AppColors.darkBg,
            ],
            stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
          ),
        ),
        child: Stack(
          children: [
            _buildAnimatedBackground(),
            _buildFloatingShapes(),
            SafeArea(
              child: _isLoading
                  ? _buildLoadingScreen()
                  : _buildProfileContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return Stack(
      children: List.generate(15, (index) {
        return AnimatedBuilder(
          animation: _rotationController,
          builder: (context, child) {
            final offset = Offset(
              (index * 80.0 + _rotationController.value * 150) % MediaQuery.of(context).size.width,
              (index * 50.0 + _rotationController.value * 80) % MediaQuery.of(context).size.height,
            );

            return Positioned(
              left: offset.dx,
              top: offset.dy,
              child: Container(
                width: 2 + (index % 4) * 1.0,
                height: 2 + (index % 4) * 1.0,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: [
                    AppColors.primaryGreen,
                    AppColors.primaryPurple,
                    const Color(0xFFff3e9d),
                    const Color(0xFF0e8aff),
                    Colors.white,
                  ][index % 5].withOpacity(0.3),
                  boxShadow: [
                    BoxShadow(
                      color: [
                        AppColors.primaryGreen,
                        AppColors.primaryPurple,
                        const Color(0xFFff3e9d),
                        const Color(0xFF0e8aff),
                        Colors.white,
                      ][index % 5].withOpacity(0.2),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }),
    );
  }

  Widget _buildFloatingShapes() {
    return Stack(
      children: List.generate(6, (index) {
        return AnimatedBuilder(
          animation: _pulseController,
          builder: (context, child) {
            final size = MediaQuery.of(context).size;
            final offset = Offset(
              (index * 120.0 + _pulseController.value * 60) % size.width,
              (index * 100.0 + math.sin(_pulseController.value * 2 * math.pi + index) * 40) % size.height,
            );

            return Positioned(
              left: offset.dx,
              top: offset.dy,
              child: Transform.rotate(
                angle: _pulseController.value * 2 * math.pi + index,
                child: Container(
                  width: 25 + (index % 3) * 10,
                  height: 25 + (index % 3) * 10,
                  decoration: BoxDecoration(
                    shape: index % 2 == 0 ? BoxShape.circle : BoxShape.rectangle,
                    borderRadius: index % 2 == 1 ? BorderRadius.circular(6) : null,
                    border: Border.all(
                      color: [
                        AppColors.primaryGreen,
                        AppColors.primaryPurple,
                        const Color(0xFFff3e9d),
                        const Color(0xFF0e8aff),
                      ][index % 4].withOpacity(0.2),
                      width: 1.5,
                    ),
                    gradient: LinearGradient(
                      colors: [
                        [
                          AppColors.primaryGreen,
                          AppColors.primaryPurple,
                          const Color(0xFFff3e9d),
                          const Color(0xFF0e8aff),
                        ][index % 4].withOpacity(0.1),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }

  Widget _buildLoadingScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedBuilder(
            animation: _pulseController,
            builder: (context, child) {
              return Transform.scale(
                scale: 1.0 + _pulseController.value * 0.1,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primaryGreen,
                        AppColors.primaryPurple,
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryGreen.withOpacity(0.3),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 40,
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 20),
          const Text(
            'প্রোফাইল লোড হচ্ছে...',
            style: TextStyle(
              color: AppColors.textPrimary,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          _buildHeader(),
          const SizedBox(height: 30),
          _buildProfileCard(),
          const SizedBox(height: 20),
          _buildStatsCard(),
          const SizedBox(height: 20),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        IconButton(
          onPressed: () => Navigator.pop(context),
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.primaryGreen.withOpacity(0.3),
              ),
            ),
            child: const Icon(
              Icons.arrow_back,
              color: AppColors.textPrimary,
              size: 20,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'প্রোফাইল',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              Text(
                'আপনার ব্যক্তিগত তথ্য পরিচালনা করুন',
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.textPrimary.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProfileCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.primaryGreen.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Stack(
        children: [
          Column(
            children: [
              _buildProfileAvatar(),
              const SizedBox(height: 16),
              _buildSincereBadgeNotice(),
              const SizedBox(height: 24),
              _buildProfileForm(),
            ],
          ),
          // Edit button in top right corner
          Positioned(
            top: 0,
            right: 0,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _isEditing = !_isEditing;
                  if (_isEditing) {
                    // Clear previous errors and validate current form
                    _validationErrors.clear();
                    _validateForm();
                  } else {
                    // Clear validation state when exiting edit mode
                    _validationErrors.clear();
                    _isFormValid = false;
                  }
                });
                HapticFeedback.lightImpact();
              },
              child: AnimatedBuilder(
                animation: _pulseController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _isEditing ? 1.0 + _pulseController.value * 0.1 : 1.0,
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        gradient: _isEditing
                            ? LinearGradient(
                                colors: [
                                  AppColors.primaryGreen,
                                  AppColors.primaryGreen.withBlue(100),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              )
                            : LinearGradient(
                                colors: [
                                  AppColors.primaryGreen.withOpacity(0.8),
                                  AppColors.primaryPurple.withOpacity(0.8),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: _isEditing
                              ? Colors.white.withOpacity(0.3)
                              : AppColors.primaryGreen.withOpacity(0.5),
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: _isEditing
                                ? AppColors.primaryGreen.withOpacity(0.4)
                                : AppColors.primaryGreen.withOpacity(0.3),
                            blurRadius: _isEditing ? 12 : 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _isEditing ? Icons.close : Icons.edit,
                            color: Colors.white,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _isEditing ? 'বন্ধ' : 'সম্পাদনা',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileAvatar() {
    final user = _authService.currentUser;
    final userName = _userData?['name'] ?? user?.displayName ?? 'ব্যবহারকারী';
    final userEmail = user?.email ?? '';

    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Column(
          children: [
            Stack(
              children: [
                Transform.scale(
                  scale: 1.0 + _pulseController.value * 0.05,
                  child: Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          AppColors.primaryGreen,
                          AppColors.primaryPurple,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primaryGreen.withOpacity(0.3),
                          blurRadius: 20,
                          spreadRadius: 5,
                        ),
                      ],
                    ),
                    child: FutureBuilder<String?>(
                      future: _authService.getProfilePictureUrl(),
                      builder: (context, snapshot) {
                        final photoURL = snapshot.data ?? user?.photoURL;
                        return photoURL != null
                            ? ClipOval(
                                child: Image.network(
                                  photoURL,
                                  width: 100,
                                  height: 100,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return _buildDefaultAvatar(userName);
                                  },
                                ),
                              )
                            : _buildDefaultAvatar(userName);
                      },
                    ),
                  ),
                ),
                // Camera button for updating profile picture
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: GestureDetector(
                    onTap: _showProfilePictureOptions,
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.primaryGreen,
                        border: Border.all(
                          color: AppColors.darkBg,
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primaryGreen.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.camera_alt,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              userName,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              userEmail,
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary.withOpacity(0.7),
              ),
            ),
            if (user?.emailVerified == true) ...[
              const SizedBox(height: 8),
              _buildBadgesRow(),
            ],
          ],
        );
      },
    );
  }

  Widget _buildDefaultAvatar(String name) {
    return Center(
      child: Text(
        name.isNotEmpty ? name[0].toUpperCase() : 'U',
        style: const TextStyle(
          fontSize: 40,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildBadgesRow() {
    return StreamBuilder<DocumentSnapshot>(
      stream: FirebaseFirestore.instance
          .collection('users')
          .doc(_authService.currentUser?.uid)
          .snapshots(),
      builder: (context, snapshot) {
        // Calculate profile completion
        bool isProfileComplete = false;
        if (snapshot.hasData && snapshot.data!.exists) {
          final userData = snapshot.data!.data() as Map<String, dynamic>?;
          if (userData != null) {
            int completedFields = 0;
            int totalFields = 4;

            if (userData['name'] != null && userData['name'].toString().isNotEmpty) completedFields++;
            if (userData['phone'] != null && userData['phone'].toString().isNotEmpty) completedFields++;
            if (userData['address'] != null && userData['address'].toString().isNotEmpty) completedFields++;
            if (userData['profession'] != null && userData['profession'].toString().isNotEmpty) completedFields++;

            int profileCompletion = ((completedFields / totalFields) * 100).round();
            isProfileComplete = profileCompletion >= 100;
          }
        }

        return Wrap(
          spacing: 8,
          runSpacing: 8,
          alignment: WrapAlignment.center,
          children: [
            // Verified badge
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.primaryGreen.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.primaryGreen.withOpacity(0.5),
                ),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.verified,
                    color: AppColors.primaryGreen,
                    size: 16,
                  ),
                  SizedBox(width: 4),
                  Text(
                    'যাচাইকৃত',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.primaryGreen,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            // Connected badge (only show when profile is complete)
            if (isProfileComplete)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.amber.withOpacity(0.8),
                      Colors.orange.withOpacity(0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.amber.withOpacity(0.7),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.amber.withOpacity(0.3),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.link,
                      color: Colors.white,
                      size: 16,
                    ),
                    SizedBox(width: 4),
                    Text(
                      'Connected',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildSincereBadgeNotice() {
    return StreamBuilder<DocumentSnapshot>(
      stream: FirebaseFirestore.instance
          .collection('users')
          .doc(_authService.currentUser?.uid)
          .snapshots(),
      builder: (context, snapshot) {
        if (!snapshot.hasData || !snapshot.data!.exists) {
          return const SizedBox.shrink();
        }

        final userData = snapshot.data!.data() as Map<String, dynamic>?;
        if (userData == null) return const SizedBox.shrink();

        // Calculate profile completion - only count user-editable fields
        int completedFields = 0;
        int totalFields = 4; // Only count user-editable fields: name, phone, address, profession

        // User-editable profile fields (25% each)
        if (userData['name'] != null && userData['name'].toString().isNotEmpty) completedFields++;
        if (userData['phone'] != null && userData['phone'].toString().isNotEmpty) completedFields++;
        if (userData['address'] != null && userData['address'].toString().isNotEmpty) completedFields++;
        if (userData['profession'] != null && userData['profession'].toString().isNotEmpty) completedFields++;

        int profileCompletion = ((completedFields / totalFields) * 100).round();
        bool isProfileComplete = profileCompletion >= 100;

        if (isProfileComplete) {
          // Hide notice when profile is complete - badge will show beside verified badge
          return const SizedBox.shrink();
        } else {
          // Show notice to complete profile for Connected badge
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primaryGreen.withOpacity(0.1),
                  AppColors.primaryPurple.withOpacity(0.1),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.primaryGreen.withOpacity(0.3),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryGreen.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.primaryGreen.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.primaryGreen.withOpacity(0.5),
                    ),
                  ),
                  child: Icon(
                    Icons.verified_user_outlined,
                    color: AppColors.primaryGreen,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '🔗 Connected ব্যাজ পেতে',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryGreen,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'প্রোফাইল সম্পূর্ণ করুন ($profileCompletion% সম্পন্ন)',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textPrimary.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.primaryGreen.withOpacity(0.7),
                  size: 16,
                ),
              ],
            ),
          );
        }
      },
    );
  }

  Widget _buildProfileForm() {
    return Column(
      children: [
        _buildFormField(
          controller: _nameController,
          label: 'নাম',
          icon: Icons.person_outline,
          enabled: _isEditing,
          errorText: _validationErrors['name'],
        ),
        const SizedBox(height: 16),
        _buildFormField(
          controller: _phoneController,
          label: 'ফোন নম্বর',
          icon: Icons.phone_outlined,
          enabled: _isEditing,
          keyboardType: TextInputType.phone,
          errorText: _validationErrors['phone'],
        ),
        const SizedBox(height: 16),
        _buildFormField(
          controller: _addressController,
          label: 'ঠিকানা',
          icon: Icons.location_on_outlined,
          enabled: _isEditing,
          maxLines: 2,
          errorText: _validationErrors['address'],
        ),
        const SizedBox(height: 16),
        _buildFormField(
          controller: _professionController,
          label: 'পেশা',
          icon: Icons.work_outline,
          enabled: _isEditing,
          errorText: _validationErrors['profession'],
        ),
        if (_isEditing) ...[
          const SizedBox(height: 24),
          _buildSaveButton(),
        ],
      ],
    );
  }

  Widget _buildFormField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required bool enabled,
    TextInputType? keyboardType,
    int maxLines = 1,
    bool Function(dynamic)? validator,
    String? helperText,
    String? errorText,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: enabled
            ? [
                BoxShadow(
                  color: AppColors.primaryGreen.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextFormField(
            controller: controller,
            enabled: enabled,
            keyboardType: keyboardType,
            maxLines: maxLines,
            style: TextStyle(
              color: enabled ? AppColors.textPrimary : AppColors.textPrimary.withOpacity(0.7),
              fontSize: 16,
            ),
            decoration: InputDecoration(
              labelText: label,
              labelStyle: TextStyle(
                color: enabled
                    ? (errorText != null ? Colors.red : AppColors.primaryGreen.withOpacity(0.8))
                    : AppColors.textPrimary.withOpacity(0.5),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.all(12),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: enabled
                      ? (errorText != null ? Colors.red.withOpacity(0.1) : AppColors.primaryGreen.withOpacity(0.1))
                      : Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: enabled
                      ? (errorText != null ? Colors.red : AppColors.primaryGreen)
                      : Colors.grey,
                  size: 20,
                ),
              ),
              suffixIcon: enabled && controller.text.isNotEmpty
                  ? Icon(
                      errorText != null ? Icons.error : Icons.check_circle,
                      color: errorText != null ? Colors.red : Colors.green,
                      size: 20,
                    )
                  : null,
              errorText: errorText,
              errorStyle: const TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: enabled
                      ? (errorText != null ? Colors.red : AppColors.primaryGreen.withOpacity(0.2))
                      : Colors.grey.withOpacity(0.2),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: errorText != null ? Colors.red : AppColors.primaryGreen.withOpacity(0.2),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: errorText != null ? Colors.red : AppColors.primaryGreen,
                  width: 2
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Colors.red),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.grey.withOpacity(0.2),
                ),
              ),
              filled: true,
              fillColor: enabled
                  ? Colors.white.withOpacity(0.03)
                  : Colors.grey.withOpacity(0.05),
              contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            ),
          ),
          if (helperText != null && enabled) ...[
            const SizedBox(height: 4),
            Padding(
              padding: const EdgeInsets.only(left: 12),
              child: Text(
                helperText,
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.textPrimary.withOpacity(0.6),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    // Use the validation state from _validateForm method
    bool isFormValid = _isFormValid;

    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 + _pulseController.value * 0.02,
          child: Container(
            width: double.infinity,
            height: 50,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: isFormValid
                  ? LinearGradient(
                      colors: [
                        AppColors.primaryGreen,
                        AppColors.primaryGreen.withBlue(100),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                  : LinearGradient(
                      colors: [
                        Colors.grey.withOpacity(0.5),
                        Colors.grey.withOpacity(0.3),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
              boxShadow: isFormValid
                  ? [
                      BoxShadow(
                        color: AppColors.primaryGreen.withOpacity(0.3),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ]
                  : [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
            ),
            child: ElevatedButton(
              onPressed: (_isLoading || !isFormValid) ? null : _updateUserData,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                shadowColor: Colors.transparent,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.save,
                    color: isFormValid ? Colors.white : Colors.grey.shade600,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    isFormValid ? 'সংরক্ষণ করুন' : 'সব তথ্য সঠিকভাবে পূরণ করুন',
                    style: TextStyle(
                      fontSize: isFormValid ? 16 : 14,
                      fontWeight: FontWeight.bold,
                      color: isFormValid ? Colors.white : Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatsCard() {
    return StreamBuilder<DocumentSnapshot>(
      stream: FirebaseFirestore.instance
          .collection('users')
          .doc(_authService.currentUser?.uid)
          .snapshots(),
      builder: (context, snapshot) {
        Map<String, int> stats = {
          'totalLifetimeChats': 0,
          'todayChats': 0,
          'daysSinceJoining': 0,
          'profileCompletion': 0,
          'totalCharactersUsed': 0
        };

        if (snapshot.hasData && snapshot.data!.exists) {
          final userData = snapshot.data!.data() as Map<String, dynamic>?;
          if (userData != null) {
            // Calculate stats from real-time data
            final chatHistory = userData['chatHistory'] as List<dynamic>? ?? [];
            final totalCharactersUsed = userData['totalCharactersUsed'] as int? ?? 0;
            final createdAt = userData['createdAt'] as Timestamp?;

            // Get today's chats from dailyChats map for consistency
            final now = DateTime.now();
            String today = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
            Map<String, dynamic> dailyChats = Map<String, dynamic>.from(userData['dailyChats'] ?? {});
            int todayChats = dailyChats[today] ?? 0;

            // Calculate days since joining
            int daysSinceJoining = 0;
            if (createdAt != null) {
              final joinDate = createdAt.toDate();
              daysSinceJoining = DateTime.now().difference(joinDate).inDays;
            }

            // Calculate profile completion - consistent with AuthService
            int completedFields = 0;
            int totalFields = 4; // Only count user-editable fields: name, phone, address, profession

            // User-editable profile fields (25% each)
            if (userData['name'] != null && userData['name'].toString().isNotEmpty) completedFields++;
            if (userData['phone'] != null && userData['phone'].toString().isNotEmpty) completedFields++;
            if (userData['address'] != null && userData['address'].toString().isNotEmpty) completedFields++;
            if (userData['profession'] != null && userData['profession'].toString().isNotEmpty) completedFields++;

            int profileCompletion = ((completedFields / totalFields) * 100).round();

            stats = {
              'totalLifetimeChats': chatHistory.length,
              'todayChats': todayChats,
              'daysSinceJoining': daysSinceJoining,
              'profileCompletion': profileCompletion,
              'totalCharactersUsed': totalCharactersUsed,
            };
          }
        }

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.05),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: AppColors.primaryPurple.withOpacity(0.2),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.analytics_outlined,
                    color: AppColors.primaryPurple,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'পরিসংখ্যান',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // First row: Lifetime chats and Today's chats
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'মোট চ্যাট',
                      '${stats['totalLifetimeChats']}',
                      Icons.chat_bubble_outline,
                      AppColors.primaryGreen,
                    ),
                  ),
                  Expanded(
                    child: StreamBuilder<int>(
                      stream: _authService.getTodaysChatCountStream(),
                      builder: (context, snapshot) {
                        final todayChatsRealtime = snapshot.data ?? stats['todayChats'];
                        return _buildStatItem(
                          'আজকের চ্যাট',
                          '$todayChatsRealtime',
                          Icons.today,
                          Colors.orange,
                        );
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // Second row: Days since joining and Total tokens
              Row(
                children: [
                  Expanded(
                    child: _buildStatItem(
                      'সদস্যতার দিন',
                      '${stats['daysSinceJoining']}',
                      Icons.calendar_today,
                      AppColors.primaryPurple,
                    ),
                  ),
                  Expanded(
                    child: _buildStatItem(
                      'মোট অক্ষর',
                      '${stats['totalCharactersUsed']}',
                      Icons.text_fields,
                      Colors.blue,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _buildProfileCompletion(stats['profileCompletion']!),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textPrimary.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProfileCompletion(int completion) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'প্রোফাইল সম্পূর্ণতা',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textPrimary.withOpacity(0.8),
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '$completion%',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.primaryGreen,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        AnimatedBuilder(
          animation: _pulseController,
          builder: (context, child) {
            return Container(
              height: 8,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: Colors.grey.withOpacity(0.2),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: completion / 100,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primaryGreen,
                        AppColors.primaryPurple,
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryGreen.withOpacity(0.3),
                        blurRadius: 4,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        _buildActionButton(
          label: 'সাইন আউট',
          icon: Icons.logout,
          color: Colors.orange,
          onPressed: _signOut,
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required String label,
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: double.infinity,
      height: 50,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
        color: color.withOpacity(0.1),
      ),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }




}