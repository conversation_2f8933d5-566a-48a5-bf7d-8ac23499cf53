// Firebase Storage Rules for UkilGiri App
// Copy these rules to your Firebase Console -> Storage -> Rules

rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Allow authenticated users to upload profile pictures
    match /profile_pictures/{userId}.jpg {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow authenticated users to read profile pictures
    match /profile_pictures/{allPaths=**} {
      allow read: if request.auth != null;
    }
    
    // Test folder for connectivity checks
    match /test/{allPaths=**} {
      allow read: if request.auth != null;
    }
  }
}

// Instructions:
// 1. Go to Firebase Console
// 2. Select your project (ukilgiri-app)
// 3. Go to Storage
// 4. Click on "Rules" tab
// 5. Replace the existing rules with the rules above
// 6. Click "Publish"

// Current issue: Storage rules are likely set to deny all uploads
// This will fix the "object-not-found" error during uploads
